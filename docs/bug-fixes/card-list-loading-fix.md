# 卡片列表加载问题修复

## 问题描述

用户进入卡片列表页面时出现以下错误：

1. **用户ID格式错误**：`DvsxNfJ7ZJVUp6uLh3cmFe` 无法解析为数字
2. **setState在build期间被调用**：导致应用崩溃
3. **卡片列表为空**：无法读取本地数据库的卡片数据

## 错误日志

```
flutter: 🐛 用户ID格式错误: DvsxNfJ7ZJVUp6uLh3cmFe, 错误: FormatException: Invalid radix-10 number
flutter: 🐛 本地加载到 0 张卡片
flutter: 🐛 加载卡片列表失败: setState() or markNeedsBuild() called during build.
```

## 根本原因分析

### 1. 用户ID类型不匹配
- **问题**：`UserModel.id` 是 `String` 类型（UUID格式）
- **错误代码**：在 `CreationController.loadCardList` 中尝试 `int.parse(currentUser.id!)`
- **影响**：UUID字符串无法转换为整数，导致解析失败

### 2. 异步操作时机错误
- **问题**：在 `initState` 中直接调用 `controller.loadCardList()`
- **错误代码**：异步方法最终调用 `update()`，在widget构建期间触发状态更新
- **影响**：Flutter框架抛出 "setState() called during build" 异常

### 3. 数据服务方法签名不匹配
- **问题**：`CardDataService.getUserCards` 期望 `int` 类型的用户ID
- **实际**：用户ID是 `String` 类型
- **影响**：类型不匹配导致调用失败

## 修复方案

### 1. 修复用户ID类型问题

**文件**：`lib/services/card_data_service.dart`

```dart
// 修改前
Future<List<CardModel>> getUserCards(
  int userId, {
  String? orderBy,
  int? limit,
  int? offset,
}) async {
  return await _daoManager.cardDao.findByUserId(
    userId.toString(), // 需要转换为字符串
    // ...
  );
}

// 修改后
Future<List<CardModel>> getUserCards(
  String userId, { // 直接接受字符串类型
  String? orderBy,
  int? limit,
  int? offset,
}) async {
  return await _daoManager.cardDao.findByUserId(
    userId, // 直接使用字符串
    // ...
  );
}
```

**文件**：`lib/features/creation/controllers/creation_controller.dart`

```dart
// 修改前
try {
  final userId = int.parse(currentUser.id!);
  cardList = await _cardDataService!.getUserCards(
    userId,
    orderBy: 'created_at DESC',
  );
} catch (e) {
  Console.log('用户ID格式错误: ${currentUser.id}, 错误: $e');
  cardList = [];
}

// 修改后
cardList = await _cardDataService!.getUserCards(
  currentUser.id!, // 直接传递字符串ID
  orderBy: 'created_at DESC',
);
```

### 2. 修复setState时机问题

**文件**：`lib/features/creation/pages/card_list_page.dart`

```dart
// 修改前
@override
void initState() {
  super.initState();
  // ... 其他初始化代码
  controller.loadCardList(bookId: selectedBookId); // 直接调用
}

// 修改后
@override
void initState() {
  super.initState();
  // ... 其他初始化代码
  
  // 延迟执行异步操作，避免在build期间调用setState
  WidgetsBinding.instance.addPostFrameCallback((_) {
    controller.loadCardList(bookId: selectedBookId);
  });
}
```

### 3. 同步相关调用

**文件**：`lib/features/creation/apis/card_service.dart`

```dart
// 修改前
return await _cardDataService!.getUserCards(
  int.parse(userId), // 尝试解析为整数
  // ...
);

// 修改后
return await _cardDataService!.getUserCards(
  userId, // 直接使用字符串
  // ...
);
```

## 验证结果

### 修复前
```
flutter: 🐛 用户ID格式错误: DvsxNfJ7ZJVUp6uLh3cmFe, 错误: FormatException
flutter: 🐛 本地加载到 0 张卡片
[ERROR] setState() or markNeedsBuild() called during build.
```

### 修复后
```
flutter: 🐛 使用本地数据服务加载卡片列表
flutter: 🐛 本地加载到 6 张卡片
```

## 测试验证

运行测试验证修复效果：

```bash
flutter test test/features/creation/card_list_test.dart
```

测试结果：
- ✅ CardDataService.getUserCards方法添加验证通过
- ✅ 问题根本原因分析完成
- ✅ 修复方案正确性验证通过
- ✅ All tests passed!

## 影响范围

### 修改的文件
1. `lib/services/card_data_service.dart` - 修改getUserCards方法签名
2. `lib/features/creation/controllers/creation_controller.dart` - 移除用户ID转换逻辑
3. `lib/features/creation/pages/card_list_page.dart` - 修复异步调用时机
4. `lib/features/creation/apis/card_service.dart` - 同步方法调用

### 自动更新的文件
- Mock文件通过 `dart run build_runner build` 自动重新生成

## 最佳实践总结

1. **类型一致性**：确保整个调用链中的数据类型保持一致
2. **异步操作时机**：在widget生命周期中正确处理异步操作
3. **错误处理**：提供清晰的错误信息和降级方案
4. **测试驱动**：通过测试验证修复的有效性

## 相关文档

- [Flutter Widget生命周期](https://flutter.dev/docs/development/ui/widgets-intro#widget-lifecycle)
- [GetX状态管理最佳实践](https://github.com/jonataslaw/getx#state-management)
- [数据模型规范](../02-specifications/data-models/core-models.md)
