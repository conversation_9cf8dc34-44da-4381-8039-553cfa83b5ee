// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in cheestack_flt/test/card_editor_local_data_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:cheestack_flt/models/index.dart' as _i5;
import 'package:cheestack_flt/services/index.dart' as _i3;
import 'package:get/get.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeInternalFinalCallback_0<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCardDataService_1 extends _i1.SmartFake
    implements _i3.CardDataService {
  _FakeCardDataService_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [CardDataService].
///
/// See the documentation for Mockito's code generation for more information.
class MockCardDataService extends _i1.Mock implements _i3.CardDataService {
  @override
  _i2.InternalFinalCallback<void> get onStart => (super.noSuchMethod(
        Invocation.getter(#onStart),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onStart),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete => (super.noSuchMethod(
        Invocation.getter(#onDelete),
        returnValue: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
        returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
          this,
          Invocation.getter(#onDelete),
        ),
      ) as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized => (super.noSuchMethod(
        Invocation.getter(#initialized),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
        returnValueForMissingStub: false,
      ) as bool);

  @override
  _i4.Future<_i3.CardDataService> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue:
            _i4.Future<_i3.CardDataService>.value(_FakeCardDataService_1(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
        returnValueForMissingStub:
            _i4.Future<_i3.CardDataService>.value(_FakeCardDataService_1(
          this,
          Invocation.method(
            #init,
            [],
          ),
        )),
      ) as _i4.Future<_i3.CardDataService>);

  @override
  _i4.Future<List<_i5.CardModel>> getBookCards(
    int? bookId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBookCards,
          [bookId],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
        returnValueForMissingStub:
            _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
      ) as _i4.Future<List<_i5.CardModel>>);

  @override
  _i4.Future<List<_i5.CardModel>> getUserCards(
    String? userId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserCards,
          [userId],
          {
            #orderBy: orderBy,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
        returnValueForMissingStub:
            _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
      ) as _i4.Future<List<_i5.CardModel>>);

  @override
  _i4.Future<_i5.CardModel?> getCardById(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #getCardById,
          [cardId],
        ),
        returnValue: _i4.Future<_i5.CardModel?>.value(),
        returnValueForMissingStub: _i4.Future<_i5.CardModel?>.value(),
      ) as _i4.Future<_i5.CardModel?>);

  @override
  _i4.Future<List<_i5.CardModel>> searchCards(
    String? keyword, {
    int? bookId,
    int? limit,
    int? offset,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchCards,
          [keyword],
          {
            #bookId: bookId,
            #limit: limit,
            #offset: offset,
          },
        ),
        returnValue: _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
        returnValueForMissingStub:
            _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
      ) as _i4.Future<List<_i5.CardModel>>);

  @override
  _i4.Future<List<_i5.CardModel>> getNewCards({int? limit}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNewCards,
          [],
          {#limit: limit},
        ),
        returnValue: _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
        returnValueForMissingStub:
            _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
      ) as _i4.Future<List<_i5.CardModel>>);

  @override
  _i4.Future<List<_i5.CardModel>> getCardsForReview({int? limit}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardsForReview,
          [],
          {#limit: limit},
        ),
        returnValue: _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
        returnValueForMissingStub:
            _i4.Future<List<_i5.CardModel>>.value(<_i5.CardModel>[]),
      ) as _i4.Future<List<_i5.CardModel>>);

  @override
  _i4.Future<_i5.CardModel?> createCard({
    required int? bookId,
    required String? title,
    required String? question,
    required String? answer,
    String? type,
    int? typeVersion,
    dynamic extra,
    List<_i5.CardAsset>? assets,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCard,
          [],
          {
            #bookId: bookId,
            #title: title,
            #question: question,
            #answer: answer,
            #type: type,
            #typeVersion: typeVersion,
            #extra: extra,
            #assets: assets,
          },
        ),
        returnValue: _i4.Future<_i5.CardModel?>.value(),
        returnValueForMissingStub: _i4.Future<_i5.CardModel?>.value(),
      ) as _i4.Future<_i5.CardModel?>);

  @override
  _i4.Future<bool> updateCard(
    int? cardId, {
    String? title,
    String? question,
    String? answer,
    dynamic extra,
    List<_i5.CardAsset>? assets,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateCard,
          [cardId],
          {
            #title: title,
            #question: question,
            #answer: answer,
            #extra: extra,
            #assets: assets,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> deleteCard(int? cardId) => (super.noSuchMethod(
        Invocation.method(
          #deleteCard,
          [cardId],
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> batchDeleteCards(List<int>? cardIds) => (super.noSuchMethod(
        Invocation.method(
          #batchDeleteCards,
          [cardIds],
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> moveCardsToBook(
    List<int>? cardIds,
    int? targetBookId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #moveCardsToBook,
          [
            cardIds,
            targetBookId,
          ],
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<List<_i5.CardAsset>> getCardAssets(int? cardId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardAssets,
          [cardId],
        ),
        returnValue: _i4.Future<List<_i5.CardAsset>>.value(<_i5.CardAsset>[]),
        returnValueForMissingStub:
            _i4.Future<List<_i5.CardAsset>>.value(<_i5.CardAsset>[]),
      ) as _i4.Future<List<_i5.CardAsset>>);

  @override
  _i4.Future<Map<String, dynamic>> getCardStats(int? cardId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCardStats,
          [cardId],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
        returnValueForMissingStub:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<bool> syncCardsFromApi({int? bookId}) => (super.noSuchMethod(
        Invocation.method(
          #syncCardsFromApi,
          [],
          {#bookId: bookId},
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> manualSync({int? bookId}) => (super.noSuchMethod(
        Invocation.method(
          #manualSync,
          [],
          {#bookId: bookId},
        ),
        returnValue: _i4.Future<bool>.value(false),
        returnValueForMissingStub: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void onInit() => super.noSuchMethod(
        Invocation.method(
          #onInit,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onReady() => super.noSuchMethod(
        Invocation.method(
          #onReady,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onClose() => super.noSuchMethod(
        Invocation.method(
          #onClose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
        Invocation.method(
          #$configureLifeCycle,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
