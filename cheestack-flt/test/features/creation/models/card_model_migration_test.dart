import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/features/creation/models/card_model.dart';

/// 卡片模型迁移测试 - 渐进式混合方案
/// 
/// 测试新的CardModel结构：
/// - 保持核心字段简单（questionText, answerText）
/// - 扩展内容通过CardContent数组管理
/// - 支持多媒体和选择题
void main() {
  group('CardModel 渐进式混合方案测试', () {
    group('新CardModel结构测试', () {
      test('应该支持基础文本卡片', () {
        // 🔴 这个测试现在应该失败，因为新结构还没实现
        final card = CardModel(
          id: 1,
          userId: 'user-123',
          bookId: 1,
          type: 'text',
          title: '基础卡片',
          questionText: '这是问题文本',
          answerText: '这是答案文本',
        );

        expect(card.id, equals(1));
        expect(card.userId, equals('user-123'));
        expect(card.bookId, equals(1));
        expect(card.questionText, equals('这是问题文本'));
        expect(card.answerText, equals('这是答案文本'));
        expect(card.contents, isNull); // 基础卡片不需要扩展内容
      });

      test('应该支持带扩展内容的卡片', () {
        final contents = [
          CardContent(
            id: 'content-1',
            type: 'image',
            role: 'question',
            url: '/images/question.png',
            orderIndex: 1,
          ),
          CardContent(
            id: 'content-2',
            type: 'audio',
            role: 'answer',
            url: '/audio/answer.mp3',
            orderIndex: 2,
          ),
        ];

        final card = CardModel(
          id: 2,
          userId: 'user-123',
          bookId: 1,
          type: 'multimedia',
          title: '多媒体卡片',
          questionText: '基础问题文本',
          answerText: '基础答案文本',
          contents: contents,
        );

        expect(card.contents?.length, equals(2));
        expect(card.contents?[0].type, equals('image'));
        expect(card.contents?[0].role, equals('question'));
        expect(card.contents?[1].type, equals('audio'));
        expect(card.contents?[1].role, equals('answer'));
      });

      test('应该支持选择题卡片', () {
        final choices = [
          CardContent(
            id: 'choice-1',
            type: 'choice',
            role: 'choice',
            text: '选项A',
            isCorrect: false,
            orderIndex: 1,
          ),
          CardContent(
            id: 'choice-2',
            type: 'choice',
            role: 'choice',
            text: '选项B',
            isCorrect: true,
            orderIndex: 2,
          ),
          CardContent(
            id: 'choice-3',
            type: 'choice',
            role: 'choice',
            text: '选项C',
            isCorrect: false,
            orderIndex: 3,
          ),
        ];

        final card = CardModel(
          id: 3,
          userId: 'user-123',
          bookId: 1,
          type: 'choice',
          title: '选择题卡片',
          questionText: '这是选择题问题',
          answerText: '正确答案是B',
          contents: choices,
        );

        expect(card.type, equals('choice'));
        expect(card.contents?.length, equals(3));
        
        final correctChoices = card.contents?.where((c) => c.isCorrect == true).toList();
        expect(correctChoices?.length, equals(1));
        expect(correctChoices?.first.text, equals('选项B'));
      });
    });

    group('CardContent模型测试', () {
      test('应该正确创建文本内容', () {
        final content = CardContent(
          id: 'content-1',
          type: 'text',
          role: 'question',
          text: '文本内容',
          orderIndex: 1,
        );

        expect(content.id, equals('content-1'));
        expect(content.type, equals('text'));
        expect(content.role, equals('question'));
        expect(content.text, equals('文本内容'));
        expect(content.url, isNull);
        expect(content.isCorrect, isNull);
      });

      test('应该正确创建媒体内容', () {
        final content = CardContent(
          id: 'content-2',
          type: 'image',
          role: 'answer',
          url: '/images/answer.png',
          text: '图片描述',
          orderIndex: 2,
        );

        expect(content.type, equals('image'));
        expect(content.url, equals('/images/answer.png'));
        expect(content.text, equals('图片描述'));
      });

      test('应该正确创建选择项内容', () {
        final content = CardContent(
          id: 'choice-1',
          type: 'choice',
          role: 'choice',
          text: '选择项文本',
          isCorrect: true,
          orderIndex: 1,
        );

        expect(content.type, equals('choice'));
        expect(content.role, equals('choice'));
        expect(content.isCorrect, equals(true));
      });
    });

    group('JSON序列化测试', () {
      test('CardModel应该正确序列化和反序列化', () {
        final originalCard = CardModel(
          id: 1,
          userId: 'user-123',
          bookId: 1,
          type: 'text',
          title: '测试卡片',
          questionText: '问题',
          answerText: '答案',
          contents: [
            CardContent(
              id: 'content-1',
              type: 'text',
              role: 'supplement',
              text: '补充说明',
              orderIndex: 1,
            ),
          ],
        );

        final json = originalCard.toJson();
        final deserializedCard = CardModel.fromJson(json);

        expect(deserializedCard.id, equals(originalCard.id));
        expect(deserializedCard.userId, equals(originalCard.userId));
        expect(deserializedCard.questionText, equals(originalCard.questionText));
        expect(deserializedCard.answerText, equals(originalCard.answerText));
        expect(deserializedCard.contents?.length, equals(1));
        expect(deserializedCard.contents?[0].text, equals('补充说明'));
      });

      test('CardContent应该正确序列化和反序列化', () {
        final originalContent = CardContent(
          id: 'content-1',
          type: 'image',
          role: 'question',
          text: '图片描述',
          url: '/images/test.png',
          isCorrect: true,
          orderIndex: 1,
        );

        final json = originalContent.toJson();
        final deserializedContent = CardContent.fromJson(json);

        expect(deserializedContent.id, equals(originalContent.id));
        expect(deserializedContent.type, equals(originalContent.type));
        expect(deserializedContent.role, equals(originalContent.role));
        expect(deserializedContent.text, equals(originalContent.text));
        expect(deserializedContent.url, equals(originalContent.url));
        expect(deserializedContent.isCorrect, equals(originalContent.isCorrect));
        expect(deserializedContent.orderIndex, equals(originalContent.orderIndex));
      });
    });

    group('向后兼容性测试', () {
      test('应该能够从旧的CardModel数据创建新CardModel', () {
        // 模拟旧的数据结构
        final oldCardJson = {
          'id': 1,
          'user': {'id': 'user-123', 'name': '用户'},
          'type': 'general',
          'title': '旧卡片',
          'question': '旧问题',
          'answer': '旧答案',
          'created_at': '2025-01-01T00:00:00Z',
          'updated_at': '2025-01-01T00:00:00Z',
        };

        // 应该能够处理旧数据并转换为新结构
        final card = CardModel.fromLegacyJson(oldCardJson);

        expect(card.id, equals(1));
        expect(card.userId, equals('user-123'));
        expect(card.questionText, equals('旧问题'));
        expect(card.answerText, equals('旧答案'));
        expect(card.contents, isNull); // 旧数据没有扩展内容
      });
    });
  });
}
