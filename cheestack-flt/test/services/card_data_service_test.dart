import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/services/dao/dao_manager.dart';
import 'package:cheestack_flt/services/dao/card_dao.dart';
import 'package:cheestack_flt/services/dao/card_asset_dao.dart';
import 'package:cheestack_flt/services/dao/study_record_dao.dart';

import 'card_data_service_test.mocks.dart';

@GenerateNiceMocks([
  MockSpec<DaoManager>(),
  MockSpec<CardDao>(),
  MockSpec<CardAssetDao>(),
  MockSpec<StudyRecordDao>(),
  MockSpec<UserDataService>(),
])
void main() {
  group('CardDataService 测试', () {
    late CardDataService cardDataService;
    late MockDaoManager mockDaoManager;
    late MockCardDao mockCardDao;
    late MockCardAssetDao mockCardAssetDao;
    late MockStudyRecordDao mockStudyRecordDao;
    late MockUserDataService mockUserDataService;

    setUp(() {
      // 初始化 Get
      Get.testMode = true;
      Get.reset();

      // 创建 mock 对象
      mockDaoManager = MockDaoManager();
      mockCardDao = MockCardDao();
      mockCardAssetDao = MockCardAssetDao();
      mockStudyRecordDao = MockStudyRecordDao();
      mockUserDataService = MockUserDataService();

      // 设置 mock 返回值
      when(mockDaoManager.cardDao).thenReturn(mockCardDao);
      when(mockDaoManager.cardAssetDao).thenReturn(mockCardAssetDao);
      when(mockDaoManager.studyRecordDao).thenReturn(mockStudyRecordDao);
      when(mockUserDataService.currentUser).thenReturn(UserModel(id: '1'));

      // 注册服务
      Get.put<UserDataService>(mockUserDataService, permanent: true);
      Get.put<DaoManager>(mockDaoManager, permanent: true);

      cardDataService = CardDataService();
      // 手动设置依赖
      cardDataService.onInit();
    });

    tearDown(() {
      Get.reset();
    });

    test('应该能够获取书籍的卡片', () async {
      // Arrange
      final bookId = 1;
      final expectedCards = [
        CardModel(
          id: 1,
          title: '测试卡片1',
          question: '问题1',
          answer: '答案1',
          type: 'basic',
          typeVersion: 1,
        ),
        CardModel(
          id: 2,
          title: '测试卡片2',
          question: '问题2',
          answer: '答案2',
          type: 'basic',
          typeVersion: 1,
        ),
      ];

      when(mockCardDao.findByBookId(
        bookId,
        orderBy: anyNamed('orderBy'),
        limit: anyNamed('limit'),
        offset: anyNamed('offset'),
      )).thenAnswer((_) async => expectedCards);

      // Act
      final result = await cardDataService.getBookCards(bookId);

      // Assert
      expect(result, equals(expectedCards));
      verify(mockCardDao.findByBookId(
        bookId,
        orderBy: 'created_at DESC',
        limit: null,
        offset: null,
      )).called(1);
    });

    test('应该能够根据ID获取卡片', () async {
      // Arrange
      final cardId = 1;
      final expectedCard = CardModel(
        id: cardId,
        title: '测试卡片',
        question: '测试问题',
        answer: '测试答案',
        type: 'basic',
        typeVersion: 1,
      );

      when(mockCardDao.findById(cardId)).thenAnswer((_) async => expectedCard);

      // Act
      final result = await cardDataService.getCardById(cardId);

      // Assert
      expect(result, equals(expectedCard));
      verify(mockCardDao.findById(cardId)).called(1);
    });

    test('应该能够搜索卡片', () async {
      // Arrange
      final keyword = '测试';
      final userId = '1';
      final expectedCards = [
        CardModel(
          id: 1,
          title: '测试卡片',
          question: '测试问题',
          answer: '测试答案',
          type: 'basic',
          typeVersion: 1,
        ),
      ];

      when(mockCardDao.search(
        userId,
        keyword,
        bookId: anyNamed('bookId'),
        limit: anyNamed('limit'),
        offset: anyNamed('offset'),
      )).thenAnswer((_) async => expectedCards);

      // Act
      final result = await cardDataService.searchCards(keyword);

      // Assert
      expect(result, equals(expectedCards));
      verify(mockCardDao.search(
        userId,
        keyword,
        bookId: null,
        limit: null,
        offset: null,
      )).called(1);
    });

    test('应该能够创建卡片', () async {
      // Arrange
      final bookId = 1;
      final title = '新卡片';
      final question = '新问题';
      final answer = '新答案';
      final cardId = 123;

      when(mockCardDao.insertWithIds(any, any, any))
          .thenAnswer((_) async => cardId);

      // Act
      final result = await cardDataService.createCard(
        bookId: bookId,
        title: title,
        question: question,
        answer: answer,
      );

      // Assert
      expect(result, isNotNull);
      expect(result!.id, equals(cardId));
      expect(result.title, equals(title));
      expect(result.question, equals(question));
      expect(result.answer, equals(answer));

      verify(mockCardDao.insertWithIds(any, '1', bookId)).called(1);
    });

    test('应该能够删除卡片', () async {
      // Arrange
      final cardId = 1;
      final existingCard = CardModel(
        id: cardId,
        title: '要删除的卡片',
        question: '问题',
        answer: '答案',
        type: 'basic',
        typeVersion: 1,
      );

      when(mockCardDao.findById(cardId)).thenAnswer((_) async => existingCard);
      when(mockCardAssetDao.deleteByCardId(cardId)).thenAnswer((_) async => 1);
      when(mockStudyRecordDao.deleteByCardId(cardId))
          .thenAnswer((_) async => 1);
      when(mockCardDao.delete(cardId)).thenAnswer((_) async => 1);

      // Act
      final result = await cardDataService.deleteCard(cardId);

      // Assert
      expect(result, isTrue);
      verify(mockCardDao.findById(cardId)).called(1);
      verify(mockCardAssetDao.deleteByCardId(cardId)).called(1);
      verify(mockStudyRecordDao.deleteByCardId(cardId)).called(1);
      verify(mockCardDao.delete(cardId)).called(1);
    });

    test('删除不存在的卡片应该返回false', () async {
      // Arrange
      final cardId = 999;

      when(mockCardDao.findById(cardId)).thenAnswer((_) async => null);

      // Act
      final result = await cardDataService.deleteCard(cardId);

      // Assert
      expect(result, isFalse);
      verify(mockCardDao.findById(cardId)).called(1);
      verifyNever(mockCardAssetDao.deleteByCardId(any));
      verifyNever(mockStudyRecordDao.deleteByCardId(any));
      verifyNever(mockCardDao.delete(any));
    });

    test('当用户未登录时应该返回空列表', () async {
      // Arrange
      when(mockUserDataService.currentUser).thenReturn(null);

      // Act
      final result = await cardDataService.getBookCards(1);

      // Assert
      expect(result, isEmpty);
      verifyNever(mockCardDao.findByBookId(any));
    });
  });

  group('CardDataService 渐进式混合方案测试', () {
    test('应该支持创建带内容的卡片数据结构', () {
      // 创建多媒体内容
      final contents = [
        CardContent(
          id: 'content-1',
          type: 'text',
          role: 'supplement',
          text: '补充说明文本',
          orderIndex: 1,
        ),
        CardContent(
          id: 'content-2',
          type: 'image',
          role: 'question',
          url: '/images/question.png',
          text: '问题图片',
          orderIndex: 2,
        ),
        CardContent(
          id: 'content-3',
          type: 'audio',
          role: 'answer',
          url: '/audio/answer.mp3',
          text: '答案音频',
          orderIndex: 3,
        ),
      ];

      // 创建卡片模型
      final card = CardModel(
        id: 1,
        userId: 'user-123',
        bookId: 1,
        type: 'multimedia',
        title: '多媒体卡片',
        question: '基础问题文本',
        answer: '基础答案文本',
        contents: contents,
      );

      expect(card.contents?.length, equals(3));
      expect(card.contents?[0].type, equals('text'));
      expect(card.contents?[1].type, equals('image'));
      expect(card.contents?[2].type, equals('audio'));
    });

    test('应该支持选择题卡片数据结构', () {
      final choices = [
        CardContent(
          id: 'choice-1',
          type: 'choice',
          role: 'choice',
          text: '选项A：错误答案',
          isCorrect: false,
          orderIndex: 1,
        ),
        CardContent(
          id: 'choice-2',
          type: 'choice',
          role: 'choice',
          text: '选项B：正确答案',
          isCorrect: true,
          orderIndex: 2,
        ),
        CardContent(
          id: 'choice-3',
          type: 'choice',
          role: 'choice',
          text: '选项C：错误答案',
          isCorrect: false,
          orderIndex: 3,
        ),
      ];

      final card = CardModel(
        id: 2,
        userId: 'user-123',
        bookId: 1,
        type: 'choice',
        title: '选择题卡片',
        question: '请选择正确答案',
        answer: '正确答案是B',
        contents: choices,
      );

      expect(card.type, equals('choice'));
      expect(card.contents?.length, equals(3));

      // 验证正确答案
      final correctChoices =
          card.contents?.where((c) => c.isCorrect == true).toList();
      expect(correctChoices?.length, equals(1));
      expect(correctChoices?.first.text, equals('选项B：正确答案'));
    });

    test('应该支持基础卡片（无扩展内容）', () {
      final card = CardModel(
        id: 4,
        userId: 'user-123',
        bookId: 1,
        type: 'basic',
        title: '基础卡片',
        question: '简单问题',
        answer: '简单答案',
      );

      expect(card.contents, isNull);
      expect(card.question, equals('简单问题'));
      expect(card.answer, equals('简单答案'));
      expect(card.type, equals('basic'));
    });
  });
}
