import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/features/creation/models/card_model.dart';

/// 数据库迁移测试 - 单元测试
///
/// 测试渐进式混合方案的数据模型
void main() {
  group('渐进式混合方案数据模型测试', () {
    test('应该支持新旧字段共存', () {
      final card = CardModel(
        id: 1,
        userId: 'user-123',
        bookId: 1,
        type: 'text',
        title: '测试卡片',
        // 新字段
        questionText: '新问题文本',
        answerText: '新答案文本',
        // 旧字段
        question: '旧问题',
        answer: '旧答案',
      );

      // 验证新旧字段都存在
      expect(card.id, equals(1));
      expect(card.userId, equals('user-123'));
      expect(card.bookId, equals(1));
      expect(card.questionText, equals('新问题文本'));
      expect(card.answerText, equals('新答案文本'));
      expect(card.question, equals('旧问题'));
      expect(card.answer, equals('旧答案'));
    });

    test('应该支持JSON序列化包含新字段', () {
      final card = CardModel(
        id: 1,
        userId: 'user-123',
        bookId: 1,
        questionText: '新问题',
        answerText: '新答案',
        contents: [
          CardContent(
            id: 'content-1',
            type: 'text',
            role: 'supplement',
            text: '补充说明',
            orderIndex: 1,
          ),
        ],
      );

      final json = card.toJson();

      expect(json['user_id'], equals('user-123'));
      expect(json['book_id'], equals(1));
      expect(json['question_text'], equals('新问题'));
      expect(json['answer_text'], equals('新答案'));
      expect(json['contents'], isA<List>());
      expect((json['contents'] as List?)?.length, equals(1));
    });

    test('应该支持从旧JSON格式创建CardModel', () {
      final oldJson = {
        'id': 1,
        'user': {'id': 'user-123', 'name': '用户'},
        'type': 'general',
        'title': '旧卡片',
        'question': '旧问题',
        'answer': '旧答案',
        'created_at': '2025-01-01T00:00:00Z',
        'updated_at': '2025-01-01T00:00:00Z',
      };

      final card = CardModel.fromLegacyJson(oldJson);

      expect(card.id, equals(1));
      expect(card.userId, equals('user-123'));
      expect(card.questionText, equals('旧问题'));
      expect(card.answerText, equals('旧答案'));
      expect(card.question, equals('旧问题')); // 保留旧字段
      expect(card.answer, equals('旧答案')); // 保留旧字段
    });

    test('CardContent应该支持不同类型和角色', () {
      // 文本内容
      final textContent = CardContent(
        id: 'text-1',
        type: 'text',
        role: 'question',
        text: '问题文本',
        orderIndex: 1,
      );

      // 图片内容
      final imageContent = CardContent(
        id: 'image-1',
        type: 'image',
        role: 'answer',
        url: '/images/answer.png',
        text: '图片描述',
        orderIndex: 2,
      );

      // 选择项内容
      final choiceContent = CardContent(
        id: 'choice-1',
        type: 'choice',
        role: 'choice',
        text: '选项A',
        isCorrect: true,
        orderIndex: 3,
      );

      expect(textContent.type, equals('text'));
      expect(textContent.role, equals('question'));
      expect(imageContent.type, equals('image'));
      expect(imageContent.url, equals('/images/answer.png'));
      expect(choiceContent.isCorrect, equals(true));
    });
  });
}
