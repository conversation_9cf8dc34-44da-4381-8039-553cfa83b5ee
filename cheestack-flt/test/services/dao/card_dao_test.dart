import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/features/creation/models/card_model.dart';

/// CardDao 渐进式混合方案测试
/// 
/// 测试新的DAO层对CardContent的支持
void main() {
  group('CardDao 渐进式混合方案测试', () {
    test('应该支持创建带内容的卡片模型', () {
      final contents = [
        CardContent(
          id: 'content-1',
          type: 'text',
          role: 'supplement',
          text: '补充说明',
          orderIndex: 1,
        ),
        CardContent(
          id: 'content-2',
          type: 'image',
          role: 'question',
          url: '/images/question.png',
          text: '图片描述',
          orderIndex: 2,
        ),
      ];

      final card = CardModel(
        id: 1,
        userId: 'user-123',
        bookId: 1,
        type: 'multimedia',
        title: '多媒体卡片',
        question: '这是问题',
        answer: '这是答案',
        contents: contents,
      );

      expect(card.contents?.length, equals(2));
      expect(card.contents?[0].type, equals('text'));
      expect(card.contents?[0].role, equals('supplement'));
      expect(card.contents?[1].type, equals('image'));
      expect(card.contents?[1].role, equals('question'));
    });

    test('应该支持选择题卡片', () {
      final choices = [
        CardContent(
          id: 'choice-1',
          type: 'choice',
          role: 'choice',
          text: '选项A',
          isCorrect: false,
          orderIndex: 1,
        ),
        CardContent(
          id: 'choice-2',
          type: 'choice',
          role: 'choice',
          text: '选项B',
          isCorrect: true,
          orderIndex: 2,
        ),
        CardContent(
          id: 'choice-3',
          type: 'choice',
          role: 'choice',
          text: '选项C',
          isCorrect: false,
          orderIndex: 3,
        ),
      ];

      final card = CardModel(
        id: 2,
        userId: 'user-123',
        bookId: 1,
        type: 'choice',
        title: '选择题',
        question: '选择正确答案',
        answer: '正确答案是B',
        contents: choices,
      );

      expect(card.type, equals('choice'));
      expect(card.contents?.length, equals(3));
      
      final correctChoices = card.contents?.where((c) => c.isCorrect == true).toList();
      expect(correctChoices?.length, equals(1));
      expect(correctChoices?.first.text, equals('选项B'));
    });

    test('CardContent应该支持不同的内容类型', () {
      // 文本内容
      final textContent = CardContent(
        id: 'text-1',
        type: 'text',
        role: 'question',
        text: '这是文本内容',
        orderIndex: 1,
      );

      // 图片内容
      final imageContent = CardContent(
        id: 'image-1',
        type: 'image',
        role: 'answer',
        url: '/images/answer.png',
        text: '图片描述',
        orderIndex: 2,
      );

      // 音频内容
      final audioContent = CardContent(
        id: 'audio-1',
        type: 'audio',
        role: 'supplement',
        url: '/audio/pronunciation.mp3',
        text: '发音音频',
        orderIndex: 3,
      );

      // 选择项内容
      final choiceContent = CardContent(
        id: 'choice-1',
        type: 'choice',
        role: 'choice',
        text: '选择项',
        isCorrect: true,
        orderIndex: 4,
      );

      expect(textContent.type, equals('text'));
      expect(textContent.role, equals('question'));
      expect(textContent.text, equals('这是文本内容'));

      expect(imageContent.type, equals('image'));
      expect(imageContent.url, equals('/images/answer.png'));

      expect(audioContent.type, equals('audio'));
      expect(audioContent.url, equals('/audio/pronunciation.mp3'));

      expect(choiceContent.type, equals('choice'));
      expect(choiceContent.isCorrect, equals(true));
    });

    test('CardContent应该支持序列化', () {
      final content = CardContent(
        id: 'content-1',
        cardId: 123,
        type: 'image',
        role: 'question',
        text: '图片描述',
        url: '/images/test.png',
        isCorrect: false,
        orderIndex: 1,
        createdAt: DateTime(2025, 1, 1),
      );

      final json = content.toJson();
      
      expect(json['id'], equals('content-1'));
      expect(json['card_id'], equals(123));
      expect(json['type'], equals('image'));
      expect(json['role'], equals('question'));
      expect(json['text'], equals('图片描述'));
      expect(json['url'], equals('/images/test.png'));
      expect(json['is_correct'], equals(false));
      expect(json['order_index'], equals(1));

      // 测试反序列化
      final deserializedContent = CardContent.fromJson(json);
      expect(deserializedContent.id, equals(content.id));
      expect(deserializedContent.cardId, equals(content.cardId));
      expect(deserializedContent.type, equals(content.type));
      expect(deserializedContent.role, equals(content.role));
      expect(deserializedContent.text, equals(content.text));
      expect(deserializedContent.url, equals(content.url));
      expect(deserializedContent.isCorrect, equals(content.isCorrect));
      expect(deserializedContent.orderIndex, equals(content.orderIndex));
    });

    test('CardContent应该支持copyWith', () {
      final originalContent = CardContent(
        id: 'content-1',
        type: 'text',
        role: 'question',
        text: '原始文本',
        orderIndex: 1,
      );

      final updatedContent = originalContent.copyWith(
        text: '更新后的文本',
        role: 'answer',
        orderIndex: 2,
      );

      expect(updatedContent.id, equals(originalContent.id));
      expect(updatedContent.type, equals(originalContent.type));
      expect(updatedContent.text, equals('更新后的文本'));
      expect(updatedContent.role, equals('answer'));
      expect(updatedContent.orderIndex, equals(2));
    });

    test('应该支持基础卡片（无扩展内容）', () {
      final card = CardModel(
        id: 3,
        userId: 'user-123',
        bookId: 1,
        type: 'basic',
        title: '基础卡片',
        question: '简单问题',
        answer: '简单答案',
      );

      expect(card.contents, isNull);
      expect(card.question, equals('简单问题'));
      expect(card.answer, equals('简单答案'));
    });

    test('应该支持混合内容卡片', () {
      final mixedContents = [
        CardContent(
          id: 'text-1',
          type: 'text',
          role: 'supplement',
          text: '补充文本说明',
          orderIndex: 1,
        ),
        CardContent(
          id: 'image-1',
          type: 'image',
          role: 'question',
          url: '/images/diagram.png',
          text: '示意图',
          orderIndex: 2,
        ),
        CardContent(
          id: 'audio-1',
          type: 'audio',
          role: 'answer',
          url: '/audio/explanation.mp3',
          text: '语音解释',
          orderIndex: 3,
        ),
      ];

      final card = CardModel(
        id: 4,
        userId: 'user-123',
        bookId: 1,
        type: 'mixed',
        title: '混合内容卡片',
        question: '基础问题文本',
        answer: '基础答案文本',
        contents: mixedContents,
      );

      expect(card.contents?.length, equals(3));
      expect(card.contents?.where((c) => c.type == 'text').length, equals(1));
      expect(card.contents?.where((c) => c.type == 'image').length, equals(1));
      expect(card.contents?.where((c) => c.type == 'audio').length, equals(1));
    });
  });
}
