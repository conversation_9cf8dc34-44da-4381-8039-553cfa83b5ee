import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/features/creation/models/card_model.dart';
import 'package:cheestack_flt/services/index.dart';

/// 卡片数据迁移测试
/// 
/// 测试从旧CardModel到新CardModel的数据转换逻辑
void main() {
  group('CardMigrationService 测试', () {
    test('应该正确映射资源类型到内容类型', () {
      // 测试资源类型映射
      final testCases = [
        {'input': 'image', 'expected': 'image'},
        {'input': 'img', 'expected': 'image'},
        {'input': 'audio', 'expected': 'audio'},
        {'input': 'sound', 'expected': 'audio'},
        {'input': 'video', 'expected': 'video'},
        {'input': 'text', 'expected': 'text'},
        {'input': 'unknown', 'expected': 'text'},
        {'input': null, 'expected': 'text'},
      ];

      for (final testCase in testCases) {
        final input = testCase['input'] as String?;
        final expected = testCase['expected'] as String;
        
        // 模拟映射逻辑
        String mapAssetTypeToContentType(String? assetType) {
          switch (assetType?.toLowerCase()) {
            case 'image':
            case 'img':
              return 'image';
            case 'audio':
            case 'sound':
              return 'audio';
            case 'video':
              return 'video';
            case 'text':
              return 'text';
            default:
              return 'text';
          }
        }

        final result = mapAssetTypeToContentType(input);
        expect(result, equals(expected), reason: 'Input: $input should map to $expected');
      }
    });

    test('应该正确确定内容角色', () {
      // 测试角色确定逻辑
      String determineContentRole(CardAsset asset) {
        if (asset.isCorrect == true) {
          return 'choice';
        } else if (asset.isCorrect == false) {
          return 'choice';
        } else if (asset.type == 'image' || asset.type == 'audio') {
          return 'supplement';
        } else {
          return 'supplement';
        }
      }

      // 正确答案选项
      final correctChoice = CardAsset(
        id: 1,
        cardId: 1,
        type: 'text',
        text: '正确答案',
        isCorrect: true,
      );
      expect(determineContentRole(correctChoice), equals('choice'));

      // 错误答案选项
      final incorrectChoice = CardAsset(
        id: 2,
        cardId: 1,
        type: 'text',
        text: '错误答案',
        isCorrect: false,
      );
      expect(determineContentRole(incorrectChoice), equals('choice'));

      // 图片补充内容
      final imageAsset = CardAsset(
        id: 3,
        cardId: 1,
        type: 'image',
        url: '/images/test.png',
      );
      expect(determineContentRole(imageAsset), equals('supplement'));

      // 音频补充内容
      final audioAsset = CardAsset(
        id: 4,
        cardId: 1,
        type: 'audio',
        url: '/audio/test.mp3',
      );
      expect(determineContentRole(audioAsset), equals('supplement'));

      // 普通文本内容
      final textAsset = CardAsset(
        id: 5,
        cardId: 1,
        type: 'text',
        text: '补充说明',
      );
      expect(determineContentRole(textAsset), equals('supplement'));
    });

    test('应该正确转换CardAsset到CardContent', () {
      // 创建测试资源
      final assets = [
        CardAsset(
          id: 1,
          cardId: 123,
          type: 'image',
          text: '问题图片',
          url: '/images/question.png',
        ),
        CardAsset(
          id: 2,
          cardId: 123,
          type: 'text',
          text: '选项A',
          isCorrect: false,
        ),
        CardAsset(
          id: 3,
          cardId: 123,
          type: 'text',
          text: '选项B',
          isCorrect: true,
        ),
        CardAsset(
          id: 4,
          cardId: 123,
          type: 'audio',
          text: '发音示例',
          url: '/audio/pronunciation.mp3',
        ),
      ];

      // 模拟转换逻辑
      List<CardContent> convertAssetsToContents(List<CardAsset> assets, int cardId) {
        final contents = <CardContent>[];
        
        for (int i = 0; i < assets.length; i++) {
          final asset = assets[i];
          
          String mapAssetTypeToContentType(String? assetType) {
            switch (assetType?.toLowerCase()) {
              case 'image':
              case 'img':
                return 'image';
              case 'audio':
              case 'sound':
                return 'audio';
              case 'video':
                return 'video';
              case 'text':
                return 'text';
              default:
                return 'text';
            }
          }

          String determineContentRole(CardAsset asset) {
            if (asset.isCorrect == true) {
              return 'choice';
            } else if (asset.isCorrect == false) {
              return 'choice';
            } else if (asset.type == 'image' || asset.type == 'audio') {
              return 'supplement';
            } else {
              return 'supplement';
            }
          }
          
          final content = CardContent(
            id: 'migrated_content_${asset.id}',
            cardId: cardId,
            type: mapAssetTypeToContentType(asset.type),
            role: determineContentRole(asset),
            text: asset.text,
            url: asset.url,
            isCorrect: asset.isCorrect,
            orderIndex: i,
            createdAt: DateTime.now(),
          );
          
          contents.add(content);
        }
        
        return contents;
      }

      final contents = convertAssetsToContents(assets, 123);

      expect(contents.length, equals(4));

      // 验证图片内容
      final imageContent = contents[0];
      expect(imageContent.type, equals('image'));
      expect(imageContent.role, equals('supplement'));
      expect(imageContent.text, equals('问题图片'));
      expect(imageContent.url, equals('/images/question.png'));
      expect(imageContent.orderIndex, equals(0));

      // 验证错误选项
      final incorrectChoice = contents[1];
      expect(incorrectChoice.type, equals('text'));
      expect(incorrectChoice.role, equals('choice'));
      expect(incorrectChoice.text, equals('选项A'));
      expect(incorrectChoice.isCorrect, equals(false));
      expect(incorrectChoice.orderIndex, equals(1));

      // 验证正确选项
      final correctChoice = contents[2];
      expect(correctChoice.type, equals('text'));
      expect(correctChoice.role, equals('choice'));
      expect(correctChoice.text, equals('选项B'));
      expect(correctChoice.isCorrect, equals(true));
      expect(correctChoice.orderIndex, equals(2));

      // 验证音频内容
      final audioContent = contents[3];
      expect(audioContent.type, equals('audio'));
      expect(audioContent.role, equals('supplement'));
      expect(audioContent.text, equals('发音示例'));
      expect(audioContent.url, equals('/audio/pronunciation.mp3'));
      expect(audioContent.orderIndex, equals(3));
    });

    test('MigrationStatus枚举应该包含所有必要状态', () {
      // 验证迁移状态枚举
      expect(MigrationStatus.pending, isNotNull);
      expect(MigrationStatus.success, isNotNull);
      expect(MigrationStatus.failed, isNotNull);
      expect(MigrationStatus.alreadyUpToDate, isNotNull);
      expect(MigrationStatus.validationFailed, isNotNull);
    });

    test('CardMigrationResult应该正确计算进度', () {
      final result = CardMigrationResult();
      
      // 初始状态
      expect(result.progress, equals(0.0));
      expect(result.isSuccess, isFalse);
      expect(result.hasErrors, isFalse);

      // 设置总数和已迁移数
      result.totalCards = 10;
      result.migratedCards = 3;
      expect(result.progress, equals(0.3));

      // 完成迁移
      result.migratedCards = 10;
      result.status = MigrationStatus.success;
      expect(result.progress, equals(1.0));
      expect(result.isSuccess, isTrue);

      // 添加错误
      result.errors.add('测试错误');
      expect(result.hasErrors, isTrue);
    });

    test('应该正确处理复杂的迁移场景', () {
      // 模拟一个复杂的卡片，包含多种类型的资源
      final complexAssets = [
        // 问题相关的图片
        CardAsset(
          id: 1,
          cardId: 456,
          type: 'image',
          text: '问题示意图',
          url: '/images/question_diagram.png',
        ),
        // 选择题选项
        CardAsset(
          id: 2,
          cardId: 456,
          type: 'text',
          text: 'A. 选项一',
          isCorrect: false,
        ),
        CardAsset(
          id: 3,
          cardId: 456,
          type: 'text',
          text: 'B. 选项二',
          isCorrect: true,
        ),
        CardAsset(
          id: 4,
          cardId: 456,
          type: 'text',
          text: 'C. 选项三',
          isCorrect: false,
        ),
        // 答案解释音频
        CardAsset(
          id: 5,
          cardId: 456,
          type: 'audio',
          text: '答案解释',
          url: '/audio/explanation.mp3',
        ),
        // 补充材料
        CardAsset(
          id: 6,
          cardId: 456,
          type: 'text',
          text: '相关知识点：这是一个重要的概念...',
        ),
      ];

      // 转换为内容
      List<CardContent> convertAssetsToContents(List<CardAsset> assets, int cardId) {
        final contents = <CardContent>[];
        
        for (int i = 0; i < assets.length; i++) {
          final asset = assets[i];
          
          String mapAssetTypeToContentType(String? assetType) {
            switch (assetType?.toLowerCase()) {
              case 'image':
              case 'img':
                return 'image';
              case 'audio':
              case 'sound':
                return 'audio';
              case 'video':
                return 'video';
              case 'text':
                return 'text';
              default:
                return 'text';
            }
          }

          String determineContentRole(CardAsset asset) {
            if (asset.isCorrect == true) {
              return 'choice';
            } else if (asset.isCorrect == false) {
              return 'choice';
            } else if (asset.type == 'image' || asset.type == 'audio') {
              return 'supplement';
            } else {
              return 'supplement';
            }
          }
          
          final content = CardContent(
            id: 'migrated_content_${asset.id}',
            cardId: cardId,
            type: mapAssetTypeToContentType(asset.type),
            role: determineContentRole(asset),
            text: asset.text,
            url: asset.url,
            isCorrect: asset.isCorrect,
            orderIndex: i,
            createdAt: DateTime.now(),
          );
          
          contents.add(content);
        }
        
        return contents;
      }

      final contents = convertAssetsToContents(complexAssets, 456);

      expect(contents.length, equals(6));

      // 验证内容分类
      final imageContents = contents.where((c) => c.type == 'image').toList();
      final textContents = contents.where((c) => c.type == 'text').toList();
      final audioContents = contents.where((c) => c.type == 'audio').toList();

      expect(imageContents.length, equals(1));
      expect(textContents.length, equals(4)); // 3个选项 + 1个补充文本
      expect(audioContents.length, equals(1));

      // 验证角色分类
      final choiceContents = contents.where((c) => c.role == 'choice').toList();
      final supplementContents = contents.where((c) => c.role == 'supplement').toList();

      expect(choiceContents.length, equals(3)); // 3个选择项
      expect(supplementContents.length, equals(3)); // 1个图片 + 1个音频 + 1个文本

      // 验证正确答案
      final correctChoices = choiceContents.where((c) => c.isCorrect == true).toList();
      expect(correctChoices.length, equals(1));
      expect(correctChoices.first.text, equals('B. 选项二'));
    });
  });
}
