library models;

import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

// 导入 creation 模型以保持向后兼容
import '../features/creation/models/book_model.dart';
import '../features/creation/models/card_model.dart';

// 重新导出以保持向后兼容
export '../creation_models.dart';

part 'user.dart';
part 'boss.dart';
part 'business.dart';
part 'category.dart';
part 'clerk.dart';
part 'country.dart';
part 'geek.dart';
part 'job.dart';
part 'message.dart';
part 'profile.dart';
// part 'card.dart';
part 'study_stats.dart';
part 'refresh.dart';
// part 'book.dart'; // 移动到 features/creation/models/
// part 'user.dart';
part 'review.dart';
part 'record_model.dart';
part 'custom_select_item.dart';
part 'option.dart';
part 'app_version.dart';
part 'song_info.dart';
// part 'card_model.dart'; // 移动到 features/creation/models/
part 'pronunciation_model.dart';
part 'segment_model.dart';
part 'release_model.dart';
part 'score_model.dart';
part 'book_schedule_model.dart';
part 'study_statistics.dart';
part 'study_settings.dart';
