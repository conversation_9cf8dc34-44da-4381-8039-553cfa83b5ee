library services;

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:audio_service/audio_service.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/services/dao/dao_manager.dart';
import 'package:cheestack_flt/services/dao/study_record_dao.dart';
import 'package:cheestack_flt/services/dao/sync_record_dao.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/shared/utils/oxhttp/index.dart';
import 'package:get/get.dart' hide Response, FormData, MultipartFile;
import 'package:dio/dio.dart' show FormData, MultipartFile;
import 'package:just_audio/just_audio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

part 'storage.dart';
part 'audio_handler.dart';
part 'service_locator.dart';
part 'event.dart';
part 'database_service.dart';
part 'database_migration_service.dart';
part 'sync_service.dart';
part 'api_sync_service.dart';
part 'user_data_service.dart';
part 'book_data_service.dart';
part 'card_data_service.dart';
part 'study_data_service.dart';
part 'app_init_service.dart';
// part 'page_manager.dart';
