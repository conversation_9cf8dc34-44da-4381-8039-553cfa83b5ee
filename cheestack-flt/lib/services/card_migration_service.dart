part of services;

/// 卡片数据迁移服务 - 渐进式混合方案
/// 
/// 负责处理从旧CardModel到新CardModel的数据转换
/// 确保现有数据不丢失，平滑过渡到新的数据结构
class CardMigrationService extends GetxService {
  static CardMigrationService get to => Get.find();
  
  late final DaoManager _daoManager;
  late final DatabaseMigrationService _dbMigrationService;
  
  Future<CardMigrationService> init() async {
    // 等待依赖服务初始化完成
    while (!Get.isRegistered<DaoManager>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }
    while (!Get.isRegistered<DatabaseMigrationService>()) {
      await Future.delayed(const Duration(milliseconds: 10));
    }

    _daoManager = DaoManager.to;
    _dbMigrationService = DatabaseMigrationService.to;
    return this;
  }

  /// 执行完整的卡片数据迁移
  Future<CardMigrationResult> migrateAllCards() async {
    Console.log('开始执行卡片数据迁移...');
    
    final result = CardMigrationResult();
    
    try {
      // 1. 检查是否需要迁移
      final needsMigration = await _dbMigrationService.needsMigration();
      if (!needsMigration) {
        Console.log('数据库已是最新版本，无需迁移');
        result.status = MigrationStatus.alreadyUpToDate;
        return result;
      }

      // 2. 获取所有需要迁移的卡片
      final cardsToMigrate = await _getCardsNeedingMigration();
      result.totalCards = cardsToMigrate.length;
      Console.log('找到 ${cardsToMigrate.length} 张卡片需要迁移');

      // 3. 逐个迁移卡片
      for (final card in cardsToMigrate) {
        try {
          await _migrateIndividualCard(card);
          result.migratedCards++;
          Console.log('已迁移卡片 ${card.id}: ${card.title}');
        } catch (e) {
          result.failedCards++;
          result.errors.add('迁移卡片 ${card.id} 失败: $e');
          Console.log('迁移卡片 ${card.id} 失败: $e');
        }
      }

      // 4. 迁移卡片资源到内容
      await _migrateCardAssets();

      // 5. 验证迁移结果
      final isValid = await _validateMigration();
      if (isValid) {
        result.status = MigrationStatus.success;
        Console.log('卡片数据迁移完成');
      } else {
        result.status = MigrationStatus.validationFailed;
        result.errors.add('迁移验证失败');
      }

    } catch (e) {
      result.status = MigrationStatus.failed;
      result.errors.add('迁移过程中发生错误: $e');
      Console.log('卡片数据迁移失败: $e');
    }

    return result;
  }

  /// 获取需要迁移的卡片
  Future<List<CardModel>> _getCardsNeedingMigration() async {
    // 查找所有卡片，这些卡片可能需要迁移到新结构
    return await _daoManager.cardDao.findAll();
  }

  /// 迁移单个卡片
  Future<void> _migrateIndividualCard(CardModel card) async {
    // 检查卡片是否已经有contents数据
    final existingContents = await _daoManager.cardContentDao.findByCardId(card.id!);
    if (existingContents.isNotEmpty) {
      Console.log('卡片 ${card.id} 已有内容数据，跳过迁移');
      return;
    }

    // 获取卡片的资源
    final assets = await _daoManager.cardAssetDao.findByCardId(card.id!);
    
    // 将资源转换为内容
    final contents = await _convertAssetsToContents(assets, card.id!);
    
    // 如果有转换的内容，插入到数据库
    if (contents.isNotEmpty) {
      await _daoManager.cardContentDao.insertCardContents(contents, card.id!);
      Console.log('为卡片 ${card.id} 转换了 ${contents.length} 个内容项');
    }
  }

  /// 将CardAsset转换为CardContent
  Future<List<CardContent>> _convertAssetsToContents(List<CardAsset> assets, int cardId) async {
    final contents = <CardContent>[];
    
    for (int i = 0; i < assets.length; i++) {
      final asset = assets[i];
      
      final content = CardContent(
        id: _generateContentId(),
        cardId: cardId,
        type: _mapAssetTypeToContentType(asset.type),
        role: _determineContentRole(asset),
        text: asset.text,
        url: asset.url,
        isCorrect: asset.isCorrect,
        orderIndex: i,
        createdAt: DateTime.now(),
      );
      
      contents.add(content);
    }
    
    return contents;
  }

  /// 映射资源类型到内容类型
  String _mapAssetTypeToContentType(String? assetType) {
    switch (assetType?.toLowerCase()) {
      case 'image':
      case 'img':
        return 'image';
      case 'audio':
      case 'sound':
        return 'audio';
      case 'video':
        return 'video';
      case 'text':
        return 'text';
      default:
        return 'text'; // 默认为文本类型
    }
  }

  /// 确定内容角色
  String _determineContentRole(CardAsset asset) {
    // 根据资源的属性确定角色
    if (asset.isCorrect == true) {
      return 'choice'; // 正确答案选项
    } else if (asset.isCorrect == false) {
      return 'choice'; // 错误答案选项
    } else if (asset.type == 'image' || asset.type == 'audio') {
      return 'supplement'; // 多媒体内容作为补充
    } else {
      return 'supplement'; // 默认为补充内容
    }
  }

  /// 迁移所有卡片资源
  Future<void> _migrateCardAssets() async {
    Console.log('开始迁移卡片资源...');
    
    // 获取所有还没有对应内容的资源
    final allAssets = await _daoManager.cardAssetDao.findAll();
    
    int migratedCount = 0;
    for (final asset in allAssets) {
      if (asset.cardId != null) {
        // 检查是否已经有对应的内容
        final existingContents = await _daoManager.cardContentDao.findByCardId(asset.cardId!);
        final hasCorrespondingContent = existingContents.any((c) => 
          c.url == asset.url || c.text == asset.text
        );
        
        if (!hasCorrespondingContent) {
          // 创建对应的内容项
          final content = CardContent(
            id: _generateContentId(),
            cardId: asset.cardId!,
            type: _mapAssetTypeToContentType(asset.type),
            role: _determineContentRole(asset),
            text: asset.text,
            url: asset.url,
            isCorrect: asset.isCorrect,
            orderIndex: 0,
            createdAt: DateTime.now(),
          );
          
          await _daoManager.cardContentDao.insertCardContent(content);
          migratedCount++;
        }
      }
    }
    
    Console.log('迁移了 $migratedCount 个卡片资源');
  }

  /// 验证迁移结果
  Future<bool> _validateMigration() async {
    try {
      // 检查数据库结构
      final dbValid = await _dbMigrationService.validateMigration();
      if (!dbValid) {
        Console.log('数据库结构验证失败');
        return false;
      }

      // 检查数据完整性
      final cardCount = await _daoManager.cardDao.count();
      final contentCount = await _daoManager.cardContentDao.count();
      
      Console.log('验证结果: $cardCount 张卡片, $contentCount 个内容项');
      
      // 基本的数据完整性检查
      if (cardCount < 0 || contentCount < 0) {
        return false;
      }

      return true;
    } catch (e) {
      Console.log('验证迁移结果时出错: $e');
      return false;
    }
  }

  /// 生成内容ID
  String _generateContentId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 1000 + (timestamp % 1000)).toString();
    return 'migrated_content_$random';
  }

  /// 获取迁移状态
  Future<Map<String, dynamic>> getMigrationStatus() async {
    try {
      final needsDbMigration = await _dbMigrationService.needsMigration();
      
      if (needsDbMigration) {
        return {
          'status': 'pending',
          'message': '需要执行数据库和数据迁移',
          'database_migration_needed': true,
          'data_migration_needed': true,
        };
      }

      // 检查是否有卡片需要数据迁移
      final cardsNeedingMigration = await _getCardsNeedingMigration();
      final cardsWithoutContents = <CardModel>[];
      
      for (final card in cardsNeedingMigration) {
        final contents = await _daoManager.cardContentDao.findByCardId(card.id!);
        if (contents.isEmpty) {
          final assets = await _daoManager.cardAssetDao.findByCardId(card.id!);
          if (assets.isNotEmpty) {
            cardsWithoutContents.add(card);
          }
        }
      }

      if (cardsWithoutContents.isNotEmpty) {
        return {
          'status': 'data_migration_needed',
          'message': '需要执行数据迁移',
          'database_migration_needed': false,
          'data_migration_needed': true,
          'cards_to_migrate': cardsWithoutContents.length,
        };
      }

      return {
        'status': 'completed',
        'message': '所有迁移已完成',
        'database_migration_needed': false,
        'data_migration_needed': false,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': '检查迁移状态时出错: $e',
      };
    }
  }

  /// 回滚迁移（如果需要）
  Future<bool> rollbackMigration() async {
    try {
      Console.log('开始回滚迁移...');
      
      // 删除所有迁移生成的内容
      final allContents = await _daoManager.cardContentDao.findAll();
      final migratedContents = allContents.where((c) => 
        c.id?.startsWith('migrated_content_') == true
      ).toList();
      
      for (final content in migratedContents) {
        await _daoManager.cardContentDao.deleteContent(content.id!);
      }
      
      Console.log('回滚完成，删除了 ${migratedContents.length} 个迁移内容');
      return true;
    } catch (e) {
      Console.log('回滚迁移失败: $e');
      return false;
    }
  }
}

/// 迁移结果
class CardMigrationResult {
  MigrationStatus status = MigrationStatus.pending;
  int totalCards = 0;
  int migratedCards = 0;
  int failedCards = 0;
  List<String> errors = [];
  
  bool get isSuccess => status == MigrationStatus.success;
  bool get hasErrors => errors.isNotEmpty;
  double get progress => totalCards > 0 ? migratedCards / totalCards : 0.0;
}

/// 迁移状态枚举
enum MigrationStatus {
  pending,
  success,
  failed,
  alreadyUpToDate,
  validationFailed,
}
