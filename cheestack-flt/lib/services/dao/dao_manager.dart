import 'package:get/get.dart';
import 'user_dao.dart';
import 'book_dao.dart';
import 'card_dao.dart';
import 'card_asset_dao.dart';
import 'card_content_dao.dart';
import 'study_record_dao.dart';
import 'sync_record_dao.dart';

/// DAO管理器
/// 提供统一的数据访问接口
class DaoManager extends GetxService {
  static DaoManager get to => Get.find();
  
  late final UserDao _userDao;
  late final UserConfigDao _userConfigDao;
  late final BookDao _bookDao;
  late final CardDao _cardDao;
  late final CardAssetDao _cardAssetDao;
  late final CardContentDao _cardContentDao;
  late final StudyRecordDao _studyRecordDao;
  late final SyncRecordDao _syncRecordDao;
  
  // Getter方法
  UserDao get userDao => _userDao;
  UserConfigDao get userConfigDao => _userConfigDao;
  BookDao get bookDao => _bookDao;
  CardDao get cardDao => _cardDao;
  CardAssetDao get cardAssetDao => _cardAssetDao;
  CardContentDao get cardContentDao => _cardContentDao;
  StudyRecordDao get studyRecordDao => _studyRecordDao;
  SyncRecordDao get syncRecordDao => _syncRecordDao;
  
  Future<DaoManager> init() async {
    _userDao = UserDao();
    _userConfigDao = UserConfigDao();
    _bookDao = BookDao();
    _cardDao = CardDao();
    _cardAssetDao = CardAssetDao();
    _cardContentDao = CardContentDao();
    _studyRecordDao = StudyRecordDao();
    _syncRecordDao = SyncRecordDao();

    return this;
  }
  
  /// 获取所有DAO的列表
  List<dynamic> getAllDaos() {
    return [
      _userDao,
      _userConfigDao,
      _bookDao,
      _cardDao,
      _cardAssetDao,
      _cardContentDao,
      _studyRecordDao,
      _syncRecordDao,
    ];
  }
  
  /// 清空所有表的数据
  Future<void> clearAllData() async {
    // 按照外键依赖关系的逆序删除
    await _syncRecordDao.db.delete('sync_records');
    await _studyRecordDao.db.delete('study_records');
    await _cardAssetDao.db.delete('card_assets');
    await _cardDao.db.delete('cards');
    await _bookDao.db.delete('books');
    await _userConfigDao.db.delete('user_configs');
    await _userDao.db.delete('users');
  }
  
  /// 获取数据库统计信息
  Future<Map<String, int>> getDatabaseStats() async {
    final stats = <String, int>{};
    
    // 用户统计
    stats['users'] = await _userDao.count();
    stats['user_configs'] = await _userConfigDao.count();
    
    // 内容统计
    stats['books'] = await _bookDao.count();
    stats['cards'] = await _cardDao.count();
    stats['card_assets'] = await _cardAssetDao.count();
    
    // 学习统计
    stats['study_records'] = await _studyRecordDao.count();
    
    // 同步统计
    stats['sync_records'] = await _syncRecordDao.count();
    stats['pending_syncs'] = await _syncRecordDao.count(
      where: 'status = ?',
      whereArgs: ['pending'],
    );
    stats['failed_syncs'] = await _syncRecordDao.count(
      where: 'status = ?',
      whereArgs: ['failed'],
    );
    
    return stats;
  }
  
  /// 获取用户数据统计
  Future<Map<String, dynamic>> getUserDataStats(String userId) async {
    final stats = <String, dynamic>{};
    
    // 基础数据统计
    stats['books'] = await _bookDao.countByUserId(userId);
    stats['cards'] = await _cardDao.countByUserId(userId);
    stats['study_records'] = await _studyRecordDao.count(
      where: 'user_id = ?',
      whereArgs: [userId],
    );
    
    // 学习统计
    final studyStats = await _studyRecordDao.getUserStudyStats(userId);
    stats.addAll(studyStats);
    
    // 今日学习统计
    final todayStats = await _studyRecordDao.getTodayStudyStats(userId);
    stats.addAll(todayStats);
    
    // 学习连续天数
    stats['study_streak'] = await _studyRecordDao.getStudyStreak(userId);
    
    // 同步统计
    final syncStats = await _syncRecordDao.getSyncStats(userId);
    stats['sync_stats'] = syncStats;
    
    return stats;
  }
  
  /// 检查数据完整性
  Future<Map<String, List<String>>> checkDataIntegrity() async {
    final issues = <String, List<String>>{};
    
    // 检查孤立的用户配置
    final orphanConfigs = await _userConfigDao.db.rawQuery('''
      SELECT uc.id FROM user_configs uc
      LEFT JOIN users u ON uc.user_id = u.id
      WHERE u.id IS NULL
    ''');
    if (orphanConfigs.isNotEmpty) {
      issues['orphan_user_configs'] = orphanConfigs.map((e) => e['id'].toString()).toList();
    }
    
    // 检查孤立的书籍
    final orphanBooks = await _bookDao.db.rawQuery('''
      SELECT b.id FROM books b
      LEFT JOIN users u ON b.user_id = u.id
      WHERE u.id IS NULL
    ''');
    if (orphanBooks.isNotEmpty) {
      issues['orphan_books'] = orphanBooks.map((e) => e['id'].toString()).toList();
    }
    
    // 检查孤立的卡片
    final orphanCards = await _cardDao.db.rawQuery('''
      SELECT c.id FROM cards c
      LEFT JOIN books b ON c.book_id = b.id
      WHERE b.id IS NULL
    ''');
    if (orphanCards.isNotEmpty) {
      issues['orphan_cards'] = orphanCards.map((e) => e['id'].toString()).toList();
    }
    
    // 检查孤立的卡片资源
    final orphanAssets = await _cardAssetDao.db.rawQuery('''
      SELECT ca.id FROM card_assets ca
      LEFT JOIN cards c ON ca.card_id = c.id
      WHERE c.id IS NULL
    ''');
    if (orphanAssets.isNotEmpty) {
      issues['orphan_card_assets'] = orphanAssets.map((e) => e['id'].toString()).toList();
    }
    
    // 检查孤立的学习记录
    final orphanRecords = await _studyRecordDao.db.rawQuery('''
      SELECT sr.id FROM study_records sr
      LEFT JOIN cards c ON sr.card_id = c.id
      WHERE c.id IS NULL
    ''');
    if (orphanRecords.isNotEmpty) {
      issues['orphan_study_records'] = orphanRecords.map((e) => e['id'].toString()).toList();
    }
    
    return issues;
  }
  
  /// 修复数据完整性问题
  Future<void> fixDataIntegrity() async {
    // 删除孤立的用户配置
    await _userConfigDao.db.rawDelete('''
      DELETE FROM user_configs 
      WHERE user_id NOT IN (SELECT id FROM users)
    ''');
    
    // 删除孤立的书籍
    await _bookDao.db.rawDelete('''
      DELETE FROM books 
      WHERE user_id NOT IN (SELECT id FROM users)
    ''');
    
    // 删除孤立的卡片
    await _cardDao.db.rawDelete('''
      DELETE FROM cards 
      WHERE book_id NOT IN (SELECT id FROM books)
    ''');
    
    // 删除孤立的卡片资源
    await _cardAssetDao.db.rawDelete('''
      DELETE FROM card_assets 
      WHERE card_id NOT IN (SELECT id FROM cards)
    ''');
    
    // 删除孤立的学习记录
    await _studyRecordDao.db.rawDelete('''
      DELETE FROM study_records 
      WHERE card_id NOT IN (SELECT id FROM cards)
    ''');
    
    // 删除孤立的同步记录
    await _syncRecordDao.db.rawDelete('''
      DELETE FROM sync_records 
      WHERE user_id NOT IN (SELECT id FROM users)
    ''');
  }
  
  /// 优化数据库
  Future<void> optimizeDatabase() async {
    // 执行VACUUM命令来优化数据库
    await _userDao.db.execute('VACUUM');
    
    // 分析表以优化查询计划
    await _userDao.db.execute('ANALYZE');
  }
  
  /// 备份数据库
  Future<Map<String, List<Map<String, dynamic>>>> backupDatabase() async {
    final backup = <String, List<Map<String, dynamic>>>{};
    
    // 备份所有表的数据
    backup['users'] = await _userDao.db.query('users');
    backup['user_configs'] = await _userConfigDao.db.query('user_configs');
    backup['books'] = await _bookDao.db.query('books');
    backup['cards'] = await _cardDao.db.query('cards');
    backup['card_assets'] = await _cardAssetDao.db.query('card_assets');
    backup['study_records'] = await _studyRecordDao.db.query('study_records');
    backup['sync_records'] = await _syncRecordDao.db.query('sync_records');
    
    return backup;
  }
}
