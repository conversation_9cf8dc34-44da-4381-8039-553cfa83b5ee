import 'dart:convert';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/features/creation/models/card_model.dart';
import 'base_dao.dart';

/// 卡片数据访问对象
class CardDao extends BaseDao<CardModel> {
  @override
  String get tableName => 'cards';
  
  @override
  String get primaryKey => 'id';
  
  @override
  CardModel fromMap(Map<String, dynamic> map) {
    return CardModel(
      id: map['id'],
      userId: map['user_id'],
      bookId: map['book_id'],
      type: map['type'],
      typeVersion: map['type_version'],
      title: map['title'],
      question: map['question'],
      answer: map['answer'],
      extra: map['extra'] != null ? jsonDecode(map['extra']) : null,
      scheduleId: map['schedule_id'],
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }
  
  @override
  Map<String, dynamic> toMap(CardModel entity) {
    return {
      'id': entity.id,
      'user_id': entity.userId,
      'book_id': entity.bookId,
      'type': entity.type,
      'type_version': entity.typeVersion,
      'title': entity.title,
      'question': entity.question,
      'answer': entity.answer,
      'extra': entity.extra != null ? jsonEncode(entity.extra) : null,
      'schedule_id': entity.scheduleId,
      'created_at': entity.createdAt?.toIso8601String(),
      'updated_at': entity.updatedAt?.toIso8601String(),
    };
  }
  
  /// 插入卡片（需要用户ID和书籍ID）
  Future<int> insertWithIds(CardModel card, String userId, int bookId) async {
    final map = toMap(card);
    map['user_id'] = userId;
    map['book_id'] = bookId;
    
    // 添加时间戳
    final now = DateTime.now().toIso8601String();
    map['created_at'] = now;
    map['updated_at'] = now;
    map['is_dirty'] = 1; // 标记为未同步
    
    final id = await db.insert(tableName, map);
    
    // 添加同步记录
    await addSyncRecord('create', map['id']?.toString() ?? id.toString(), null, map);
    
    return id;
  }
  
  /// 根据书籍ID查找卡片
  Future<List<CardModel>> findByBookId(int bookId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    return await findWhere(
      where: 'book_id = ?',
      whereArgs: [bookId],
      orderBy: orderBy ?? 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 根据用户ID查找卡片
  Future<List<CardModel>> findByUserId(String userId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    return await findWhere(
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: orderBy ?? 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 根据类型查找卡片
  Future<List<CardModel>> findByType(String userId, String type, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    return await findWhere(
      where: 'user_id = ? AND type = ?',
      whereArgs: [userId, type],
      orderBy: orderBy ?? 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 搜索卡片
  Future<List<CardModel>> search(String userId, String keyword, {
    int? bookId,
    int? limit,
    int? offset,
  }) async {
    String where = 'user_id = ? AND (title LIKE ? OR question LIKE ? OR answer LIKE ?)';
    List<Object?> whereArgs = [userId, '%$keyword%', '%$keyword%', '%$keyword%'];
    
    if (bookId != null) {
      where += ' AND book_id = ?';
      whereArgs.add(bookId);
    }
    
    return await findWhere(
      where: where,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 更新卡片内容
  Future<int> updateContent(int cardId, {
    String? title,
    String? question,
    String? answer,
    dynamic extra,
  }) async {
    final updateData = <String, dynamic>{
      'updated_at': DateTime.now().toIso8601String(),
      'is_dirty': 1,
    };
    
    if (title != null) updateData['title'] = title;
    if (question != null) updateData['question'] = question;
    if (answer != null) updateData['answer'] = answer;
    if (extra != null) updateData['extra'] = jsonEncode(extra);
    
    return await db.update(
      tableName,
      updateData,
      where: 'id = ?',
      whereArgs: [cardId],
    );
  }
  
  /// 更新卡片调度ID
  Future<int> updateScheduleId(int cardId, int scheduleId) async {
    return await db.update(
      tableName,
      {
        'schedule_id': scheduleId,
        'updated_at': DateTime.now().toIso8601String(),
        'is_dirty': 1,
      },
      where: 'id = ?',
      whereArgs: [cardId],
    );
  }
  
  /// 获取书籍中的卡片数量
  Future<int> countByBookId(int bookId) async {
    return await count(
      where: 'book_id = ?',
      whereArgs: [bookId],
    );
  }
  
  /// 获取用户的卡片数量
  Future<int> countByUserId(String userId) async {
    return await count(
      where: 'user_id = ?',
      whereArgs: [userId],
    );
  }
  
  /// 获取用户指定类型的卡片数量
  Future<int> countByUserIdAndType(String userId, String type) async {
    return await count(
      where: 'user_id = ? AND type = ?',
      whereArgs: [userId, type],
    );
  }
  
  /// 删除书籍中的所有卡片
  Future<int> deleteAllByBookId(int bookId) async {
    return await db.delete(
      tableName,
      where: 'book_id = ?',
      whereArgs: [bookId],
    );
  }
  
  /// 删除用户的所有卡片
  Future<int> deleteAllByUserId(String userId) async {
    return await db.delete(
      tableName,
      where: 'user_id = ?',
      whereArgs: [userId],
    );
  }
  
  /// 批量移动卡片到其他书籍
  Future<int> batchMoveToBook(List<int> cardIds, int targetBookId) async {
    if (cardIds.isEmpty) return 0;
    
    final placeholders = cardIds.map((_) => '?').join(',');
    
    return await db.rawUpdate('''
      UPDATE $tableName 
      SET book_id = ?, updated_at = ?, is_dirty = 1
      WHERE id IN ($placeholders)
    ''', [targetBookId, DateTime.now().toIso8601String(), ...cardIds]);
  }
  
  /// 批量删除卡片
  Future<int> batchDelete(List<int> cardIds) async {
    if (cardIds.isEmpty) return 0;
    
    final placeholders = cardIds.map((_) => '?').join(',');
    
    return await db.rawDelete('''
      DELETE FROM $tableName WHERE id IN ($placeholders)
    ''', cardIds);
  }
  
  /// 获取需要复习的卡片
  Future<List<CardModel>> findCardsForReview(String userId, {
    int? limit,
  }) async {
    // 这里需要结合学习记录表来查询需要复习的卡片
    // 暂时返回所有卡片
    return await findByUserId(userId, limit: limit);
  }
  
  /// 获取新卡片（未学习过的）
  Future<List<CardModel>> findNewCards(String userId, {
    int? limit,
  }) async {
    // 查找没有学习记录的卡片
    final result = await db.rawQuery('''
      SELECT c.* FROM $tableName c
      LEFT JOIN study_records sr ON c.id = sr.card_id
      WHERE c.user_id = ? AND sr.id IS NULL
      ORDER BY c.created_at ASC
      ${limit != null ? 'LIMIT $limit' : ''}
    ''', [userId]);

    return result.map((map) => fromMap(map)).toList();
  }

  /// 获取完整的卡片信息（包含内容）- 渐进式混合方案
  Future<CardModel?> findCardWithContents(int cardId) async {
    final card = await findById(cardId);
    if (card == null) return null;

    // 获取卡片内容
    final contents = await _getCardContents(cardId);

    return card.copyWith(contents: contents);
  }

  /// 获取书籍的所有卡片（包含内容）
  Future<List<CardModel>> findBookCardsWithContents(
    int bookId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final cards = await findByBookId(bookId,
        orderBy: orderBy, limit: limit, offset: offset);

    // 为每个卡片加载内容
    final cardsWithContents = <CardModel>[];
    for (final card in cards) {
      final contents = await _getCardContents(card.id!);
      cardsWithContents.add(card.copyWith(contents: contents));
    }

    return cardsWithContents;
  }

  /// 创建完整的卡片（包含内容）
  Future<int> insertCardWithContents(
    CardModel card,
    String userId,
    int bookId, {
    List<CardContent>? contents,
  }) async {
    // 插入基础卡片
    final cardId = await insertWithIds(card, userId, bookId);

    // 插入卡片内容
    if (contents != null && contents.isNotEmpty) {
      await _insertCardContents(contents, cardId);
    }

    return cardId;
  }

  /// 更新完整的卡片（包含内容）
  Future<bool> updateCardWithContents(
    int cardId, {
    String? title,
    String? question,
    String? answer,
    dynamic extra,
    List<CardContent>? contents,
  }) async {
    try {
      // 更新基础卡片信息
      await updateContent(cardId,
          title: title, question: question, answer: answer, extra: extra);

      // 更新卡片内容
      if (contents != null) {
        // 删除旧内容
        await _deleteCardContents(cardId);
        // 插入新内容
        if (contents.isNotEmpty) {
          await _insertCardContents(contents, cardId);
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 删除卡片及其所有内容
  Future<bool> deleteCardWithContents(int cardId) async {
    try {
      // 删除卡片内容
      await _deleteCardContents(cardId);

      // 删除基础卡片
      await delete(cardId);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 私有方法：获取卡片内容
  Future<List<CardContent>> _getCardContents(int cardId) async {
    try {
      final result = await db.query(
        'card_contents',
        where: 'card_id = ?',
        whereArgs: [cardId],
        orderBy: 'order_index ASC, created_at ASC',
      );

      return result
          .map((map) => CardContent(
                id: map['id'] as String?,
                cardId: map['card_id'] as int?,
                type: map['type'] as String?,
                role: map['role'] as String?,
                text: map['text'] as String?,
                url: map['url'] as String?,
                isCorrect: (map['is_correct'] as int?) == 1,
                orderIndex: map['order_index'] as int?,
                createdAt: map['created_at'] != null
                    ? DateTime.parse(map['created_at'] as String)
                    : null,
              ))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// 私有方法：插入卡片内容
  Future<void> _insertCardContents(
      List<CardContent> contents, int cardId) async {
    for (int i = 0; i < contents.length; i++) {
      final content = contents[i];
      final contentId = content.id ?? _generateContentUUID();

      await db.insert('card_contents', {
        'id': contentId,
        'card_id': cardId,
        'type': content.type,
        'role': content.role,
        'text': content.text,
        'url': content.url,
        'is_correct': content.isCorrect == true ? 1 : 0,
        'order_index': content.orderIndex ?? i,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'is_dirty': 1,
      });
    }
  }

  /// 私有方法：删除卡片内容
  Future<void> _deleteCardContents(int cardId) async {
    await db.delete(
      'card_contents',
      where: 'card_id = ?',
      whereArgs: [cardId],
    );
  }

  /// 生成内容UUID
  String _generateContentUUID() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 1000 + (timestamp % 1000)).toString();
    return 'content_$random';
  }
}
