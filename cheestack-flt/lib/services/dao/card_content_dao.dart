import 'package:cheestack_flt/features/creation/models/card_model.dart';
import 'base_dao.dart';

/// 卡片内容数据访问对象 - 渐进式混合方案
class CardContentDao extends BaseDao<CardContent> {
  @override
  String get tableName => 'card_contents';
  
  @override
  String get primaryKey => 'id';
  
  @override
  CardContent fromMap(Map<String, dynamic> map) {
    return CardContent(
      id: map['id'],
      cardId: map['card_id'],
      type: map['type'],
      role: map['role'],
      text: map['text'],
      url: map['url'],
      isCorrect: map['is_correct'] == 1,
      orderIndex: map['order_index'],
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
    );
  }
  
  @override
  Map<String, dynamic> toMap(CardContent entity) {
    return {
      'id': entity.id,
      'card_id': entity.cardId,
      'type': entity.type,
      'role': entity.role,
      'text': entity.text,
      'url': entity.url,
      'is_correct': entity.isCorrect == true ? 1 : 0,
      'order_index': entity.orderIndex,
      'created_at': entity.createdAt?.toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'is_dirty': 1, // 标记为未同步
    };
  }
  
  /// 根据卡片ID查找内容
  Future<List<CardContent>> findByCardId(int cardId) async {
    return await findWhere(
      where: 'card_id = ?',
      whereArgs: [cardId],
      orderBy: 'order_index ASC, created_at ASC',
    );
  }
  
  /// 根据卡片ID和角色查找内容
  Future<List<CardContent>> findByCardIdAndRole(int cardId, String role) async {
    return await findWhere(
      where: 'card_id = ? AND role = ?',
      whereArgs: [cardId, role],
      orderBy: 'order_index ASC, created_at ASC',
    );
  }
  
  /// 根据卡片ID和类型查找内容
  Future<List<CardContent>> findByCardIdAndType(int cardId, String type) async {
    return await findWhere(
      where: 'card_id = ? AND type = ?',
      whereArgs: [cardId, type],
      orderBy: 'order_index ASC, created_at ASC',
    );
  }
  
  /// 插入卡片内容
  Future<String> insertCardContent(CardContent content) async {
    final map = toMap(content);
    
    // 生成UUID如果没有提供
    if (content.id == null || content.id!.isEmpty) {
      map['id'] = _generateUUID();
    }
    
    // 添加时间戳
    final now = DateTime.now().toIso8601String();
    map['created_at'] = now;
    map['updated_at'] = now;
    map['is_dirty'] = 1; // 标记为未同步
    
    await db.insert(tableName, map);
    
    // 添加同步记录
    await addSyncRecord('create', map['id'], null, map);
    
    return map['id'];
  }
  
  /// 批量插入卡片内容
  Future<List<String>> insertCardContents(List<CardContent> contents, int cardId) async {
    if (contents.isEmpty) return [];
    
    final List<String> insertedIds = [];
    
    for (int i = 0; i < contents.length; i++) {
      final content = contents[i];
      final contentWithCardId = content.copyWith(
        cardId: cardId,
        orderIndex: content.orderIndex ?? i,
      );
      
      final id = await insertCardContent(contentWithCardId);
      insertedIds.add(id);
    }
    
    return insertedIds;
  }
  
  /// 更新卡片内容
  Future<int> updateCardContent(CardContent content) async {
    final map = toMap(content);
    map['updated_at'] = DateTime.now().toIso8601String();
    map['is_dirty'] = 1; // 标记为未同步
    
    final result = await db.update(
      tableName,
      map,
      where: 'id = ?',
      whereArgs: [content.id],
    );
    
    // 添加同步记录
    await addSyncRecord('update', content.id!, null, map);
    
    return result;
  }
  
  /// 删除卡片的所有内容
  Future<int> deleteByCardId(int cardId) async {
    // 先获取要删除的内容ID用于同步记录
    final contents = await findByCardId(cardId);
    
    final result = await db.delete(
      tableName,
      where: 'card_id = ?',
      whereArgs: [cardId],
    );
    
    // 添加同步记录
    for (final content in contents) {
      final contentMap = toMap(content);
      await addSyncRecord('delete', content.id!, contentMap, contentMap);
    }
    
    return result;
  }
  
  /// 删除指定内容
  Future<int> deleteContent(String contentId) async {
    final result = await db.delete(
      tableName,
      where: 'id = ?',
      whereArgs: [contentId],
    );
    
    // 添加同步记录（删除操作需要提供被删除的数据）
    final deletedData = {'id': contentId, 'deleted': true};
    await addSyncRecord('delete', contentId, deletedData, deletedData);
    
    return result;
  }
  
  /// 删除卡片的指定类型内容
  Future<int> deleteByCardIdAndType(int cardId, String type) async {
    // 先获取要删除的内容ID用于同步记录
    final contents = await findByCardIdAndType(cardId, type);
    
    final result = await db.delete(
      tableName,
      where: 'card_id = ? AND type = ?',
      whereArgs: [cardId, type],
    );
    
    // 添加同步记录
    for (final content in contents) {
      final contentMap = toMap(content);
      await addSyncRecord('delete', content.id!, contentMap, contentMap);
    }
    
    return result;
  }
  
  /// 删除卡片的指定角色内容
  Future<int> deleteByCardIdAndRole(int cardId, String role) async {
    // 先获取要删除的内容ID用于同步记录
    final contents = await findByCardIdAndRole(cardId, role);
    
    final result = await db.delete(
      tableName,
      where: 'card_id = ? AND role = ?',
      whereArgs: [cardId, role],
    );
    
    // 添加同步记录
    for (final content in contents) {
      final contentMap = toMap(content);
      await addSyncRecord('delete', content.id!, contentMap, contentMap);
    }
    
    return result;
  }
  
  /// 获取卡片内容数量
  Future<int> countByCardId(int cardId) async {
    return await count(
      where: 'card_id = ?',
      whereArgs: [cardId],
    );
  }
  
  /// 获取卡片指定类型内容数量
  Future<int> countByCardIdAndType(int cardId, String type) async {
    return await count(
      where: 'card_id = ? AND type = ?',
      whereArgs: [cardId, type],
    );
  }
  
  /// 更新内容顺序
  Future<void> updateContentOrder(List<String> contentIds) async {
    for (int i = 0; i < contentIds.length; i++) {
      await db.update(
        tableName,
        {
          'order_index': i,
          'updated_at': DateTime.now().toIso8601String(),
          'is_dirty': 1,
        },
        where: 'id = ?',
        whereArgs: [contentIds[i]],
      );
    }
  }
  
  /// 查找选择题的正确答案
  Future<List<CardContent>> findCorrectChoices(int cardId) async {
    return await findWhere(
      where: 'card_id = ? AND type = ? AND is_correct = 1',
      whereArgs: [cardId, 'choice'],
      orderBy: 'order_index ASC',
    );
  }
  
  /// 查找选择题的所有选项
  Future<List<CardContent>> findAllChoices(int cardId) async {
    return await findWhere(
      where: 'card_id = ? AND type = ?',
      whereArgs: [cardId, 'choice'],
      orderBy: 'order_index ASC',
    );
  }
  
  /// 生成UUID（简化版本）
  String _generateUUID() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 1000 + (timestamp % 1000)).toString();
    return 'content_$random';
  }
  
  /// 检查内容是否存在
  Future<bool> contentExists(String contentId) async {
    final result = await db.query(
      tableName,
      where: 'id = ?',
      whereArgs: [contentId],
      limit: 1,
    );
    return result.isNotEmpty;
  }
  
  /// 获取卡片的多媒体内容
  Future<List<CardContent>> findMediaContents(int cardId) async {
    return await findWhere(
      where: 'card_id = ? AND type IN (?, ?, ?)',
      whereArgs: [cardId, 'image', 'audio', 'video'],
      orderBy: 'order_index ASC, created_at ASC',
    );
  }
  
  /// 获取卡片的文本内容
  Future<List<CardContent>> findTextContents(int cardId) async {
    return await findWhere(
      where: 'card_id = ? AND type = ?',
      whereArgs: [cardId, 'text'],
      orderBy: 'order_index ASC, created_at ASC',
    );
  }
}
