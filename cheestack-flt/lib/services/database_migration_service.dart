part of services;

/// 数据库迁移服务 - 渐进式混合方案
///
/// 负责管理数据库版本升级和数据迁移
/// 支持从旧的cards表结构迁移到新的渐进式混合方案
class DatabaseMigrationService extends GetxService {
  static DatabaseMigrationService get to => Get.find();

  late Database _database;

  // 数据库版本定义
  static const int _currentVersion = 2; // 新版本支持渐进式混合方案
  static const int _legacyVersion = 1; // 旧版本

  Future<DatabaseMigrationService> init(Database database) async {
    _database = database;
    return this;
  }

  /// 执行数据库迁移
  Future<void> migrate(int oldVersion, int newVersion) async {
    Console.log('开始数据库迁移: $oldVersion -> $newVersion');

    if (oldVersion < 2 && newVersion >= 2) {
      await _migrateToVersion2();
    }

    Console.log('数据库迁移完成');
  }

  /// 迁移到版本2：渐进式混合方案
  Future<void> _migrateToVersion2() async {
    Console.log('执行版本2迁移：添加渐进式混合方案支持');

    await _database.transaction((txn) async {
      // 1. 添加新字段到cards表
      await _addNewFieldsToCardsTable(txn);

      // 2. 创建card_contents表
      await _createCardContentsTable(txn);

      // 3. 迁移现有数据
      await _migrateExistingCardData(txn);

      // 4. 创建索引
      await _createNewIndexes(txn);
    });
  }

  /// 添加新字段到cards表
  Future<void> _addNewFieldsToCardsTable(Transaction txn) async {
    Console.log('添加新字段到cards表...');

    try {
      // 添加新的核心字段
      await txn.execute('ALTER TABLE cards ADD COLUMN question_text TEXT');
      await txn.execute('ALTER TABLE cards ADD COLUMN answer_text TEXT');

      Console.log('新字段添加成功');
    } catch (e) {
      // 字段可能已存在，忽略错误
      Console.log('添加字段时出现错误（可能字段已存在）: $e');
    }
  }

  /// 创建card_contents表
  Future<void> _createCardContentsTable(Transaction txn) async {
    Console.log('创建card_contents表...');

    await txn.execute('''
      CREATE TABLE IF NOT EXISTS card_contents (
        id TEXT PRIMARY KEY,
        card_id INTEGER NOT NULL,
        type TEXT NOT NULL,
        role TEXT,
        text TEXT,
        url TEXT,
        is_correct INTEGER DEFAULT 0,
        order_index INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        synced_at TEXT,
        is_dirty INTEGER DEFAULT 0,
        FOREIGN KEY (card_id) REFERENCES cards (id) ON DELETE CASCADE
      )
    ''');

    Console.log('card_contents表创建成功');
  }

  /// 迁移现有卡片数据
  Future<void> _migrateExistingCardData(Transaction txn) async {
    Console.log('迁移现有卡片数据...');

    // 获取所有现有卡片
    final cards =
        await txn.query('SELECT * FROM cards WHERE question_text IS NULL');

    Console.log('找到 ${cards.length} 张需要迁移的卡片');

    for (final card in cards) {
      await _migrateIndividualCard(txn, card);
    }

    Console.log('卡片数据迁移完成');
  }

  /// 迁移单个卡片
  Future<void> _migrateIndividualCard(
      Transaction txn, Map<String, dynamic> card) async {
    final cardId = card['id'] as int;
    final question = card['question'] as String?;
    final answer = card['answer'] as String?;

    // 更新核心字段
    await txn.update(
      'cards',
      {
        'question_text': question,
        'answer_text': answer,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [cardId],
    );

    // 如果有card_assets，转换为card_contents
    final assets = await txn.query(
      'card_assets',
      where: 'card_id = ?',
      whereArgs: [cardId],
    );

    for (final asset in assets) {
      await _convertAssetToContent(txn, cardId, asset);
    }
  }

  /// 将card_asset转换为card_content
  Future<void> _convertAssetToContent(
    Transaction txn,
    int cardId,
    Map<String, dynamic> asset,
  ) async {
    final contentId = _generateUUID();
    final assetType = asset['type'] as String?;
    final text = asset['text'] as String?;
    final url = asset['url'] as String?;
    final isCorrect = asset['is_correct'] as int?;

    // 根据asset类型确定content的role
    String role = 'supplement'; // 默认角色
    if (assetType == 'text' && isCorrect == 1) {
      role = 'choice';
    } else if (assetType == 'image' || assetType == 'audio') {
      role = 'supplement';
    }

    await txn.insert('card_contents', {
      'id': contentId,
      'card_id': cardId,
      'type': assetType ?? 'text',
      'role': role,
      'text': text,
      'url': url,
      'is_correct': isCorrect == 1 ? 1 : 0,
      'order_index': 0,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'is_dirty': 0,
    });
  }

  /// 创建新索引
  Future<void> _createNewIndexes(Transaction txn) async {
    Console.log('创建新索引...');

    try {
      await txn.execute(
          'CREATE INDEX IF NOT EXISTS idx_card_contents_card_id ON card_contents(card_id)');
      await txn.execute(
          'CREATE INDEX IF NOT EXISTS idx_card_contents_type ON card_contents(type)');
      await txn.execute(
          'CREATE INDEX IF NOT EXISTS idx_card_contents_role ON card_contents(role)');
      await txn.execute(
          'CREATE INDEX IF NOT EXISTS idx_card_contents_order ON card_contents(card_id, order_index)');
      await txn.execute(
          'CREATE INDEX IF NOT EXISTS idx_cards_question_text ON cards(question_text)');
      await txn.execute(
          'CREATE INDEX IF NOT EXISTS idx_cards_answer_text ON cards(answer_text)');

      Console.log('索引创建成功');
    } catch (e) {
      Console.log('创建索引时出现错误: $e');
    }
  }

  /// 生成UUID（简化版本）
  String _generateUUID() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 1000 + (timestamp % 1000)).toString();
    return 'content_$random';
  }

  /// 检查是否需要迁移
  Future<bool> needsMigration() async {
    try {
      // 检查是否存在新字段
      final result = await _database.query(
        'PRAGMA table_info(cards)',
      );

      final hasQuestionText =
          result.any((column) => column['name'] == 'question_text');
      final hasAnswerText =
          result.any((column) => column['name'] == 'answer_text');

      if (!hasQuestionText || !hasAnswerText) {
        return true;
      }

      // 检查是否存在card_contents表
      final tables = await _database.query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='card_contents'",
      );

      return tables.isEmpty;
    } catch (e) {
      Console.log('检查迁移需求时出现错误: $e');
      return false;
    }
  }

  /// 获取迁移状态
  Future<Map<String, dynamic>> getMigrationStatus() async {
    try {
      final needsMigration = await this.needsMigration();

      if (!needsMigration) {
        return {
          'status': 'completed',
          'message': '数据库已是最新版本',
          'version': _currentVersion,
        };
      }

      // 统计需要迁移的数据
      final cardCount =
          await _database.query('SELECT COUNT(*) as count FROM cards');
      final assetCount =
          await _database.query('SELECT COUNT(*) as count FROM card_assets');

      return {
        'status': 'pending',
        'message': '需要执行数据库迁移',
        'version': _legacyVersion,
        'target_version': _currentVersion,
        'cards_to_migrate': cardCount.first['count'],
        'assets_to_migrate': assetCount.first['count'],
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': '检查迁移状态时出现错误: $e',
      };
    }
  }

  /// 验证迁移结果
  Future<bool> validateMigration() async {
    try {
      // 检查新表和字段是否存在
      final needsMigration = await this.needsMigration();
      if (needsMigration) {
        return false;
      }

      // 检查数据完整性
      final cardCount =
          await _database.query('SELECT COUNT(*) as count FROM cards');
      final contentCount =
          await _database.query('SELECT COUNT(*) as count FROM card_contents');

      Console.log(
          '迁移验证: ${cardCount.first['count']} 张卡片, ${contentCount.first['count']} 个内容项');

      return true;
    } catch (e) {
      Console.log('验证迁移结果时出现错误: $e');
      return false;
    }
  }
}
