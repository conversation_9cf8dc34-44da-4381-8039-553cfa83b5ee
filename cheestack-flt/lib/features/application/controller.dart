part of application_page;

class ApplicationController extends GetxController {
  late final PageController pageController;

  AppVersionModel? appVersionModel;

  int currentPage = 0;

  @override
  void onInit() {
    super.onInit();
    pageController = PageController(initialPage: currentPage);
    init();
  }

  @override
  void onReady() async {
    await SupgradeController.to.checkUpdate();
    super.onReady();
  }

  void onPageChanged(int page) {
    currentPage = page;
    switch (page) {
      case 0:
        break;
      case 1:
        break;
      default:
    }
    update();
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  void init() async {
    await Global.initGromore();

    // 懒加载控制器
    Get.lazyPut<HomeController>(() => HomeController());
    Get.lazyPut<ProfileController>(() => ProfileController());
    Get.lazyPut<CreationController>(() => CreationController());
    Get.lazyPut<ListeningController>(() => ListeningController());

    // 初始化这些contooller
    Get.find<HomeController>();
    Get.find<ProfileController>();
    Get.find<CreationController>();
    Get.find<ListeningController>();

    // StudyController 现在由 StudyPage 自己管理，不需要在这里初始化
  }
}
