import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/services/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'package:cheestack_flt/shared/utils/error_handler.dart';
import '../apis/book_service.dart';

/// 书籍控制器
class BookController extends GetxController {
  final BookService _bookService = BookService();
  final ImagePicker _imagePicker = ImagePicker();

  // 本地数据服务
  BookDataService? _bookDataService;

  @override
  void onInit() {
    super.onInit();
    _initServices();
    _initFromArguments();
  }

  /// 初始化服务
  void _initServices() {
    if (Get.isRegistered<BookDataService>()) {
      _bookDataService = BookDataService.to;
    }
  }

  // 表单控制器
  final TextEditingController nameController = TextEditingController();
  final TextEditingController briefController = TextEditingController();

  // 状态变量
  bool isCreate = true;
  bool isLoading = false;
  String privacy = 'free'; // 默认为公开，与后端PrivacyType.FREE一致
  String? coverImage;
  BookModel? currentBook;



  @override
  void onClose() {
    nameController.dispose();
    briefController.dispose();
    super.onClose();
  }

  /// 从路由参数初始化
  void _initFromArguments() {
    final arguments = Get.arguments;
    if (arguments is BookModel) {
      isCreate = false;
      currentBook = arguments;
      nameController.text = currentBook?.name ?? '';
      briefController.text = currentBook?.brief ?? '';
      coverImage = currentBook?.cover;
      privacy = currentBook?.privacy ?? 'free';
      update();
    }
  }

  /// 选择封面图片
  Future<void> selectCover() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (image != null) {
        coverImage = image.path;
        update();
      }
    } catch (e) {
      ShowToast.fail('选择图片失败: $e');
    }
  }

  /// 移除封面图片
  void removeCover() {
    coverImage = null;
    update();
  }

  /// 设置隐私级别
  void setPrivacy(String? value) {
    if (value != null) {
      privacy = value;
      update();
    }
  }

  /// 保存书籍 - 本地优先
  Future<void> saveBook() async {
    if (!_validateForm()) return;

    isLoading = true;
    update();

    try {
      BookModel? result;

      // 详细的调试信息
      Console.log('开始保存书籍...');
      Console.log('BookDataService 是否可用: ${_bookDataService != null}');
      Console.log(
          'BookDataService 是否注册: ${Get.isRegistered<BookDataService>()}');

      if (isCreate) {
        // 创建新书籍 - 优先保存到本地
        if (_bookDataService != null) {
          Console.log('使用本地服务创建书籍');
          Console.log('书籍名称: ${nameController.text.trim()}');
          Console.log('书籍简介: ${briefController.text.trim()}');
          Console.log('隐私设置: $privacy');
          Console.log('封面图片: $coverImage');

          result = await _bookDataService!.createBook(
            name: nameController.text.trim(),
            brief: briefController.text.trim(),
            cover: coverImage,
            privacy: privacy,
          );

          Console.log('本地创建结果: $result');

          if (result != null) {
            ShowToast.success('书籍已保存到本地，将在同步时上传到云端');
          } else {
            throw Exception('本地保存失败：BookDataService.createBook 返回 null');
          }
        } else {
          Console.log('本地服务不可用，使用API创建书籍');
          // 如果本地服务不可用，回退到直接API调用
          final bookData = <String, dynamic>{
            'name': nameController.text.trim(),
            'brief': briefController.text.trim(),
            'privacy': privacy,
          };
          result = await _bookService.createBook(bookData, coverImage);
          ShowToast.success('书籍创建成功');
        }
      } else {
        // 更新现有书籍 - 优先更新本地
        if (_bookDataService != null && currentBook?.id != null) {
          Console.log('使用本地服务更新书籍');
          final success = await _bookDataService!.updateBook(
            currentBook!.id!,
            name: nameController.text.trim(),
            brief: briefController.text.trim(),
            cover: coverImage,
            privacy: privacy,
          );

          if (success) {
            result = currentBook!.copyWith(
              name: nameController.text.trim(),
              brief: briefController.text.trim(),
              cover: coverImage,
              privacy: privacy,
              updatedAt: DateTime.now().toIso8601String(),
            );
            ShowToast.success('书籍已更新到本地，将在同步时上传到云端');
          } else {
            throw Exception('本地更新失败');
          }
        } else {
          Console.log('本地服务不可用或书籍ID为空，使用API更新书籍');
          // 如果本地服务不可用，回退到直接API调用
          final bookData = <String, dynamic>{
            'name': nameController.text.trim(),
            'brief': briefController.text.trim(),
            'privacy': privacy,
            'id': currentBook?.id,
          };
          result = await _bookService.updateBook(bookData, coverImage);
          ShowToast.success('书籍更新成功');
        }
      }

      // 返回结果
      Get.back(result: result);
    } catch (e) {
      Console.log('保存书籍时发生错误: $e');
      Console.log('错误堆栈: ${StackTrace.current}');
      final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
      ShowToast.fail(friendlyMessage);
    } finally {
      isLoading = false;
      update();
    }
  }



  /// 验证表单
  bool _validateForm() {
    if (nameController.text.trim().isEmpty) {
      ShowToast.fail('请输入书籍名称');
      return false;
    }

    if (nameController.text.trim().length > 64) {
      ShowToast.fail('书籍名称不能超过64个字符');
      return false;
    }

    if (briefController.text.trim().length > 500) {
      ShowToast.fail('书籍简介不能超过500个字符');
      return false;
    }

    return true;
  }

  /// 删除书籍
  Future<void> deleteBook() async {
    if (currentBook == null) return;

    final confirmed = await _showDeleteConfirmDialog();
    if (!confirmed) return;

    try {
      await _bookService.deleteBook(currentBook!.id!);
      ShowToast.success('书籍删除成功');
      Get.back(result: 'deleted');
    } catch (e) {
      ShowToast.fail('删除失败: $e');
    }
  }

  /// 显示删除确认对话框
  Future<bool> _showDeleteConfirmDialog() async {
    return await Get.dialog<bool>(
          AlertDialog(
            title: const OxText('确认删除'),
            content: OxText('确定要删除书籍"${currentBook?.name}"吗？此操作不可撤销。'),
            actions: [
              TextButton(
                onPressed: () => Get.back(result: false),
                child: const OxText('取消'),
              ),
              TextButton(
                onPressed: () => Get.back(result: true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(Get.context!).colorScheme.error,
                ),
                child: const OxText('删除'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// 复制书籍
  Future<void> duplicateBook() async {
    if (currentBook == null) return;

    try {
      final bookData = {
        'name': '${currentBook!.name} - 副本',
        'brief': currentBook!.brief,
        'privacy': currentBook!.privacy,
      };

      final result =
          await _bookService.createBook(bookData, currentBook!.cover);
      ShowToast.success('书籍复制成功');
      Get.back(result: result);
    } catch (e) {
      ShowToast.fail('复制失败: $e');
    }
  }
}
