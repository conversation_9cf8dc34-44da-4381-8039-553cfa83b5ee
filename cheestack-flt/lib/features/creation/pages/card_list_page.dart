import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/widgets/index.dart';
import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/shared/utils/refresh_controller_factory.dart';
import '../controllers/creation_controller.dart';
import 'widgets/search_bar.dart';

/// 卡片管理列表页面
class CardListPage extends StatefulWidget {
  const CardListPage({super.key});

  @override
  State<CardListPage> createState() => _CardListPageState();
}

class _CardListPageState extends State<CardListPage>
    with AutomaticKeepAliveClientMixin {
  late CreationController controller;
  late RefreshController refreshController;
  late String _controllerKey;
  int? selectedBookId;
  String? selectedCardType;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _controllerKey = 'card_list_${hashCode}';
    refreshController = RefreshControllerFactory.create(key: _controllerKey);
    if (!Get.isRegistered<CreationController>()) {
      Get.put<CreationController>(CreationController());
    }
    controller = Get.find<CreationController>();

    // 检查是否从书籍管理页面传入了bookId
    final arguments = Get.arguments;
    if (arguments is Map && arguments['bookId'] != null) {
      selectedBookId = arguments['bookId'];
    }

    // 延迟执行异步操作，避免在build期间调用setState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadCardList(bookId: selectedBookId);
    });
  }

  @override
  void dispose() {
    RefreshControllerFactory.dispose(_controllerKey);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GetBuilder<CreationController>(
      builder: (ctrl) {
        return Scaffold(
          appBar: _buildAppBar(ctrl),
          body: SafeArea(
            child: _buildView(ctrl),
          ),
          floatingActionButton: _buildFloatingActionButton(ctrl),
        );
      },
    );
  }

  AppBar _buildAppBar(CreationController ctrl) {
    return AppBar(
      title: OxText(selectedBookId != null ? '书籍卡片管理' : '卡片管理'),
      centerTitle: true,
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(60.h),
        child: Padding(
          padding: EdgeInsets.all(AppTheme.paddingMedium.left),
          child: CreationSearchBarWithFilter(
            hintText: '搜索卡片...',
            onChanged: ctrl.onCardSearchChanged,
            onFilterTap: _showFilterDialog,
            hasActiveFilter: selectedBookId != null,
          ),
        ),
      ),
    );
  }

  Widget _buildView(CreationController ctrl) {
    return Column(
      children: [
        if (selectedBookId != null) _buildBookInfo(),
        Expanded(
          child:
              ctrl.cardList.isEmpty ? _buildEmptyView() : _buildCardList(ctrl),
        ),
      ],
    );
  }

  Widget _buildBookInfo() {
    return Container(
      margin: EdgeInsets.all(AppTheme.paddingMedium.left),
      padding: EdgeInsets.all(AppTheme.paddingMedium.left),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(
            Icons.book,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
          SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: OxText(
              '正在管理书籍中的卡片',
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
          TextButton(
            onPressed: () {
              selectedBookId = null;
              controller.loadCardList();
            },
            child: OxText(
              '查看全部',
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.credit_card_outlined,
            size: 64.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: AppTheme.spacingMedium),
          OxText(
            selectedBookId != null ? '这本书还没有卡片' : '还没有卡片',
            fontSize: AppTheme.fontTitle,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: AppTheme.spacingSmall),
          OxText(
            '点击右下角按钮创建您的第一张卡片',
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCardList(CreationController ctrl) {
    return SmartRefresher(
      controller: refreshController,
      onRefresh: _onRefresh,
      onLoading: _onLoadMore,
      enablePullUp: true,
      enablePullDown: true,
      child: ListView.builder(
        padding: EdgeInsets.all(AppTheme.paddingMedium.left),
        itemCount: ctrl.cardList.length,
        itemBuilder: (context, index) {
          final card = ctrl.cardList[index];
          return _buildCardItem(card, index, ctrl);
        },
      ),
    );
  }

  Future<void> _onRefresh() async {
    try {
      await controller.loadCardList(bookId: selectedBookId);
      refreshController.refreshCompleted();
    } catch (e) {
      refreshController.refreshFailed();
    }
  }

  Future<void> _onLoadMore() async {
    try {
      final moreCards = await controller.loadMoreCards(bookId: selectedBookId);

      if (moreCards.isEmpty) {
        refreshController.loadNoData();
      } else {
        controller.cardList.addAll(moreCards);
        controller.update();
        refreshController.loadComplete();
      }
    } catch (e) {
      refreshController.loadFailed();
    }
  }

  Widget _buildCardItem(CardModel card, int index, CreationController ctrl) {
    return Card(
      margin: EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Slidable(
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          children: [
            SlidableAction(
              onPressed: (context) => ctrl.editCard(card),
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              icon: Icons.edit,
              label: '编辑',
            ),
            SlidableAction(
              onPressed: (context) => ctrl.deleteCard(card),
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
              icon: Icons.delete,
              label: '删除',
            ),
          ],
        ),
        child: ListTile(
          contentPadding: EdgeInsets.all(AppTheme.paddingMedium.left),
          title: OxText(
            card.title ?? '未命名卡片',
            fontSize: AppTheme.fontLarge,
            fontWeight: FontWeight.bold,
            maxLines: 2,
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: AppTheme.spacingSmall),
              if (card.question != null && card.question!.isNotEmpty) ...[
                OxText(
                  '问题: ${card.question}',
                  fontSize: AppTheme.fontSmall,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  maxLines: 2,
                ),
                SizedBox(height: 4.h),
              ],
              if (card.answer != null && card.answer!.isNotEmpty) ...[
                OxText(
                  '答案: ${card.answer}',
                  fontSize: AppTheme.fontSmall,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  maxLines: 2,
                ),
                SizedBox(height: 4.h),
              ],
              Row(
                children: [
                  Icon(
                    Icons.category,
                    size: 16.sp,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(width: 4.w),
                  OxText(
                    card.type ?? 'general',
                    fontSize: AppTheme.fontSmall,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(width: AppTheme.spacingMedium),
                  Icon(
                    Icons.access_time,
                    size: 16.sp,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(width: 4.w),
                  OxText(
                    _formatTime(card.createdAt),
                    fontSize: AppTheme.fontSmall,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
            ],
          ),
          trailing: PopupMenuButton(
            icon: Icon(
              Icons.more_vert,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'view',
                child: Row(
                  children: [
                    const Icon(Icons.visibility),
                    SizedBox(width: 8.w),
                    const OxText('查看详情'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    const Icon(Icons.copy),
                    SizedBox(width: 8.w),
                    const OxText('复制'),
                  ],
                ),
              ),
              if (selectedBookId != null)
                PopupMenuItem(
                  value: 'remove',
                  child: Row(
                    children: [
                      const Icon(Icons.remove_circle_outline),
                      SizedBox(width: 8.w),
                      const OxText('从书籍移除'),
                    ],
                  ),
                ),
            ],
            onSelected: (value) {
              switch (value) {
                case 'view':
                  ctrl.viewCardDetail(card);
                  break;
                case 'duplicate':
                  ctrl.duplicateCard(card);
                  break;
                case 'remove':
                  ctrl.removeCardFromBook(card);
                  break;
              }
            },
          ),
          onTap: () => ctrl.viewCardDetail(card),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(CreationController ctrl) {
    return FloatingActionButton.extended(
      onPressed: () => ctrl.createCard(bookId: selectedBookId),
      icon: const Icon(Icons.add),
      label: const OxText('创建卡片'),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const OxText('过滤选项'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const OxText(
                '按书籍过滤',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              SizedBox(height: AppTheme.spacingSmall),
              _buildBookFilterOptions(),
              SizedBox(height: AppTheme.spacingMedium),
              const OxText(
                '按卡片类型过滤',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              SizedBox(height: AppTheme.spacingSmall),
              _buildCardTypeFilterOptions(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearFilters();
            },
            child: const OxText('清除过滤'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const OxText('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _applyFilters();
            },
            child: const OxText('应用'),
          ),
        ],
      ),
    );
  }

  Widget _buildBookFilterOptions() {
    return GetBuilder<CreationController>(
      builder: (ctrl) {
        if (ctrl.bookList.isEmpty) {
          return const OxText('暂无书籍');
        }

        return Column(
          children: [
            ListTile(
              title: const OxText('全部书籍'),
              leading: Radio<int?>(
                value: null,
                groupValue: selectedBookId,
                onChanged: (value) {
                  setState(() {
                    selectedBookId = value;
                  });
                },
              ),
              contentPadding: EdgeInsets.zero,
            ),
            ...ctrl.bookList.map((book) => ListTile(
                  title: OxText(book.name ?? '未命名书籍'),
                  leading: Radio<int?>(
                    value: book.id,
                    groupValue: selectedBookId,
                    onChanged: (value) {
                      setState(() {
                        selectedBookId = value;
                      });
                    },
                  ),
                  contentPadding: EdgeInsets.zero,
                )),
          ],
        );
      },
    );
  }

  Widget _buildCardTypeFilterOptions() {
    final cardTypes = [
      {'value': null, 'label': '全部类型'},
      {'value': 'text', 'label': '文本卡片'},
      {'value': 'image', 'label': '图片卡片'},
      {'value': 'audio', 'label': '音频卡片'},
      {'value': 'video', 'label': '视频卡片'},
    ];

    return Column(
      children: cardTypes
          .map((type) => ListTile(
                title: OxText(type['label'] as String),
                leading: Radio<String?>(
                  value: type['value'],
                  groupValue: selectedCardType,
                  onChanged: (value) {
                    setState(() {
                      selectedCardType = value;
                    });
                  },
                ),
                contentPadding: EdgeInsets.zero,
              ))
          .toList(),
    );
  }

  void _clearFilters() {
    setState(() {
      selectedBookId = null;
      selectedCardType = null;
    });
    controller.loadCardList();
  }

  void _applyFilters() {
    controller.loadCardList(
      bookId: selectedBookId,
      cardType: selectedCardType,
    );
  }

  String _formatTime(DateTime? time) {
    if (time == null) return '未知';
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inDays > 0) {
      return '${diff.inDays}天前';
    } else if (diff.inHours > 0) {
      return '${diff.inHours}小时前';
    } else if (diff.inMinutes > 0) {
      return '${diff.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
