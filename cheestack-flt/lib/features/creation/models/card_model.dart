import 'package:collection/collection.dart';
import 'package:cheestack_flt/models/index.dart';

/// 渐进式混合方案的CardModel
///
/// 核心设计原则：
/// 1. 保持核心字段简单：questionText, answerText
/// 2. 扩展内容通过contents数组管理
/// 3. 保持向后兼容性
class CardModel {
  CardModel({
    this.id,
    this.userId,
    this.bookId,
    this.user, // 保留用于向后兼容
    this.createdAt,
    this.updatedAt,
    this.type,
    this.typeVersion,
    this.title,
    // 核心内容字段
    this.question,
    this.answer,
    // 扩展内容数组
    this.contents,
    this.notes,
    this.extra,
    this.scheduleId,
    this.cardAssets,
    this.isReviewd = false,
  });

  // 基础字段
  int? id;
  String? userId; // 新增：用户ID字符串
  int? bookId; // 新增：书籍ID
  UserModel? user; // 保留：用于向后兼容
  DateTime? createdAt;
  DateTime? updatedAt;
  String? type;
  int? typeVersion;
  String? title;

  // 核心内容字段 - 渐进式混合方案
  String? question; // 问题文本
  String? answer; // 答案文本

  // 扩展内容数组 - 支持多媒体和复杂内容
  List<CardContent>? contents;
  String? notes;
  dynamic extra;
  int? scheduleId;
  List<CardAsset>? cardAssets;
  bool isReviewd;

  bool get isNew => id == null;

  @override
  String toString() {
    return 'CardModel(id: $id, user: $user, createdAt: $createdAt, updatedAt: $updatedAt, type: $type, typeVersion: $typeVersion, title: $title, question: $question, answer: $answer, notes: $notes, extra: $extra, scheduleId: $scheduleId, cardAssets: $cardAssets)';
  }

  factory CardModel.fromJson(Map<String, Object?> json) => CardModel(
        id: json['id'] as int?,
        userId: json['user_id']?.toString(),
        bookId: json['book_id'] as int?,
        user: json['user'] == null
            ? null
            : UserModel.fromJson(json['user']! as Map<String, Object?>),
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at']!.toString()),
        updatedAt: json['updated_at'] == null
            ? null
            : DateTime.parse(json['updated_at']!.toString()),
        type: json['type']?.toString(),
        typeVersion: json['type_version'] as int? ?? 1,
        title: json['title']?.toString(),
        // 核心字段
        question: json['question']?.toString(),
        answer: json['answer']?.toString(),
        contents: (json['contents'] as List<dynamic>?)
            ?.map((e) => CardContent.fromJson(e as Map<String, Object?>))
            .toList(),
        notes: json['notes']?.toString(),
        extra: json['extra'] as dynamic,
        scheduleId: json['schedule_id'] as int?,
        cardAssets: (json['card_assets'] as List<dynamic>?)
            ?.map((e) => CardAsset.fromJson(e as Map<String, Object?>))
            .toList(),
      );

  /// 从旧版本数据创建CardModel（向后兼容）
  factory CardModel.fromLegacyJson(Map<String, Object?> json) {
    // 处理旧版本的user字段
    String? userId;
    if (json['user'] is Map) {
      userId = (json['user'] as Map)['id']?.toString();
    } else if (json['user_id'] != null) {
      userId = json['user_id']?.toString();
    }

    return CardModel(
      id: json['id'] as int?,
      userId: userId,
      bookId: json['book_id'] as int?,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at']!.toString()),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at']!.toString()),
      type: json['type']?.toString(),
      typeVersion: json['type_version'] as int? ?? 1,
      title: json['title']?.toString(),
      // 核心字段
      question: json['question']?.toString(),
      answer: json['answer']?.toString(),
      notes: json['notes']?.toString(),
      extra: json['extra'] as dynamic,
      scheduleId: json['schedule_id'] as int?,
    );
  }

  Map<String, Object?> toJson() => {
        'id': id,
        'user_id': userId,
        'book_id': bookId,
        'user': user?.toJson(),
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'type': type,
        'type_version': typeVersion,
        'title': title,
        // 核心字段
        'question': question,
        'answer': answer,
        'contents': contents?.map((e) => e.toJson()).toList(),
        'notes': notes,
        'extra': extra,
        'schedule_id': scheduleId,
        'card_assets': cardAssets?.map((e) => e.toJson()).toList(),
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! CardModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^
      user.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      type.hashCode ^
      typeVersion.hashCode ^
      title.hashCode ^
      question.hashCode ^
      answer.hashCode ^
      notes.hashCode ^
      extra.hashCode ^
      scheduleId.hashCode ^
      cardAssets.hashCode;

  /// 创建一个新的CardModel实例，但可以更新部分属性
  CardModel copyWith({
    int? id,
    String? userId,
    int? bookId,
    UserModel? user,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? type,
    int? typeVersion,
    String? title,
    String? question,
    String? answer,
    List<CardContent>? contents,
    String? notes,
    dynamic extra,
    int? scheduleId,
    List<CardAsset>? cardAssets,
  }) {
    return CardModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      bookId: bookId ?? this.bookId,
      user: user ?? this.user,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      typeVersion: typeVersion ?? this.typeVersion,
      title: title ?? this.title,
      question: question ?? this.question,
      answer: answer ?? this.answer,
      contents: contents ?? this.contents,
      notes: notes ?? this.notes,
      extra: extra ?? this.extra,
      scheduleId: scheduleId ?? this.scheduleId,
      cardAssets: cardAssets ?? this.cardAssets,
    );
  }
}

class CardAsset {
  CardAsset({
    this.id,
    this.cardId,
    this.assetId,
    this.isCorrect,
    this.type,
    this.text,
    this.url,
    this.name,
  });

  int? id;
  int? cardId;
  int? assetId;
  bool? isCorrect;
  String? type;
  String? text;
  String? url;
  String? name;

  @override
  String toString() {
    return 'CardAsset(id: $id, cardId: $cardId, assetId: $assetId, isCorrect: $isCorrect, type: $type, text: $text, url: $url, name: $name)';
  }

  factory CardAsset.fromJson(Map<String, Object?> json) => CardAsset(
        id: json['id'] as int?,
        cardId: json['card_id'] as int?,
        assetId: json['asset_id'] as int?,
        isCorrect: json['is_correct'] as bool? ?? true,
        type: json['type']?.toString(),
        text: json['text']?.toString(),
        url: json['url']?.toString(),
        name: json['name']?.toString(),
      );

  Map<String, Object?> toJson() => {
        'id': id,
        'card_id': cardId,
        'asset_id': assetId,
        'is_correct': isCorrect,
        'type': type,
        'text': text,
        'url': url,
        'name': name,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! CardAsset) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^
      cardId.hashCode ^
      assetId.hashCode ^
      isCorrect.hashCode ^
      type.hashCode ^
      text.hashCode ^
      url.hashCode ^
      name.hashCode;
}

/// 卡片内容模型 - 渐进式混合方案的核心
///
/// 支持多种内容类型和角色：
/// - type: "text", "image", "audio", "video", "choice"
/// - role: "question", "answer", "choice", "supplement", "hint"
class CardContent {
  CardContent({
    this.id,
    this.cardId,
    this.type,
    this.role,
    this.text,
    this.url,
    this.isCorrect,
    this.orderIndex,
    this.createdAt,
  });

  String? id; // UUID格式
  int? cardId; // 关联的卡片ID
  String? type; // 内容类型: text, image, audio, video, choice
  String? role; // 内容角色: question, answer, choice, supplement, hint
  String? text; // 文本内容
  String? url; // 媒体文件URL
  bool? isCorrect; // 选择题时标记是否为正确答案
  int? orderIndex; // 显示顺序
  DateTime? createdAt; // 创建时间

  @override
  String toString() {
    return 'CardContent(id: $id, cardId: $cardId, type: $type, role: $role, text: $text, url: $url, isCorrect: $isCorrect, orderIndex: $orderIndex)';
  }

  factory CardContent.fromJson(Map<String, Object?> json) => CardContent(
        id: json['id']?.toString(),
        cardId: json['card_id'] as int?,
        type: json['type']?.toString(),
        role: json['role']?.toString(),
        text: json['text']?.toString(),
        url: json['url']?.toString(),
        isCorrect: json['is_correct'] as bool?,
        orderIndex: json['order_index'] as int?,
        createdAt: json['created_at'] == null
            ? null
            : DateTime.parse(json['created_at']!.toString()),
      );

  Map<String, Object?> toJson() => {
        'id': id,
        'card_id': cardId,
        'type': type,
        'role': role,
        'text': text,
        'url': url,
        'is_correct': isCorrect,
        'order_index': orderIndex,
        'created_at': createdAt?.toIso8601String(),
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! CardContent) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^
      cardId.hashCode ^
      type.hashCode ^
      role.hashCode ^
      text.hashCode ^
      url.hashCode ^
      isCorrect.hashCode ^
      orderIndex.hashCode ^
      createdAt.hashCode;

  /// 创建一个新的CardContent实例，但可以更新部分属性
  CardContent copyWith({
    String? id,
    int? cardId,
    String? type,
    String? role,
    String? text,
    String? url,
    bool? isCorrect,
    int? orderIndex,
    DateTime? createdAt,
  }) {
    return CardContent(
      id: id ?? this.id,
      cardId: cardId ?? this.cardId,
      type: type ?? this.type,
      role: role ?? this.role,
      text: text ?? this.text,
      url: url ?? this.url,
      isCorrect: isCorrect ?? this.isCorrect,
      orderIndex: orderIndex ?? this.orderIndex,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class CardModelCreate {
  CardModelCreate({
    this.id,
    this.bookId,
    this.type,
    this.typeVersion,
    this.title,
    this.question,
    this.answer,
    this.questionAiVoice = false,
    this.answerAiVoice = false,
    this.extra,
    List<CardAsset>? cardAssets,
  }) : cardAssets = cardAssets ?? []; // 确保 cardAssets 不是 null
  int? id;
  int? bookId;
  String? type;
  int? typeVersion;
  String? title;
  String? question;
  String? answer;
  bool questionAiVoice;
  bool answerAiVoice;
  Map<String, dynamic>? extra;
  List<CardAsset> cardAssets; // 更改为非 null 类型

  @override
  String toString() {
    return 'CardModelCreate(id: $id, bookId: $bookId, type: $type, typeVersion: $typeVersion, title: $title, question: $question, answer: $answer, questionAiVoice: $questionAiVoice, answerAiVoice: $answerAiVoice, extra: $extra, cardAssets: $cardAssets)';
  }

  factory CardModelCreate.fromJson(Map<String, Object?> json) {
    return CardModelCreate(
      id: json['id'] as int?,
      bookId: json['book_id'] as int?,
      type: json['type']?.toString() ?? 'general',
      typeVersion: json['type_version'] as int? ?? 1,
      title: json['title']?.toString() ?? '',
      question: json['question']?.toString() ?? '',
      answer: json['answer']?.toString() ?? '',
      questionAiVoice: json['question_ai_voice'] as bool? ?? false,
      answerAiVoice: json['answer_ai_voice'] as bool? ?? false,
      extra: json['extra'] as Map<String, dynamic>?,
      cardAssets: (json['card_assets'] as List<dynamic>?)
              ?.map((e) => CardAsset.fromJson(e as Map<String, Object?>))
              .toList() ??
          [], // 确保 cardAssets 不为 null
    );
  }

  Map<String, Object?> toJson() => {
        'id': id,
        'book_id': bookId,
        'type': type,
        'type_version': typeVersion,
        'title': title,
        'question': question,
        'answer': answer,
        'question_ai_voice': questionAiVoice,
        'answer_ai_voice': answerAiVoice,
        'extra': extra,
        'card_assets':
            cardAssets.map((e) => e.toJson()).toList(), // cardAssets 不为 null
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! CardModelCreate) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^
      bookId.hashCode ^
      type.hashCode ^
      typeVersion.hashCode ^
      title.hashCode ^
      question.hashCode ^
      answer.hashCode ^
      extra.hashCode ^
      cardAssets.hashCode;
}

class AssetModel {
  AssetModel({this.id, this.type, this.name, this.url});

  int? id;
  String? type;
  String? name;
  String? url;

  @override
  String toString() {
    return 'AssetModel(id: $id, type: $type, name: $name, url: $url)';
  }

  factory AssetModel.fromJson(Map<String, Object?> json) => AssetModel(
        id: json['id'] as int?,
        type: json['type']?.toString(),
        name: json['name']?.toString(),
        url: json['url']?.toString(),
      );

  Map<String, Object?> toJson() => {
        'id': id,
        'type': type,
        'name': name,
        'url': url,
      };

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! AssetModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^ type.hashCode ^ name.hashCode ^ url.hashCode;
}
