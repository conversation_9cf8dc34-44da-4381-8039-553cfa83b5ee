import 'package:collection/collection.dart';
import 'package:cheestack_flt/models/index.dart';

class BookModel {
  BookModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.name,
    this.brief,
    this.cover,
    this.privacy,
    this.user,
  });

  int? id;
  String? createdAt;
  String? updatedAt;
  String? name;
  String? brief;
  String? cover;
  String? privacy;
  UserModel? user;

  factory BookModel.fromJson(Map<String, Object?> json) => BookModel(
        id: json['id'] as int?,
        createdAt: json['created_at']?.toString(),
        updatedAt: json['updated_at']?.toString(),
        name: json['name']?.toString(),
        brief: json['brief']?.toString(),
        cover: json['cover']?.toString(),
        privacy: json['privacy']?.toString(),
        user: json['user'] == null
            ? null
            : UserModel.fromJson(json['user']! as Map<String, Object?>),
      );

  Map<String, Object?> toJson() => {
        'id': id,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'name': name,
        'brief': brief,
        'cover': cover,
        'privacy': privacy,
        'user': user?.toJson(),
      };

  BookModel copyWith({
    int? id,
    String? createdAt,
    String? updatedAt,
    String? name,
    String? brief,
    String? cover,
    String? privacy,
    UserModel? user,
  }) {
    return BookModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      brief: brief ?? this.brief,
      cover: cover ?? this.cover,
      privacy: privacy ?? this.privacy,
      user: user ?? this.user,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! BookModel) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode ^
      name.hashCode ^
      brief.hashCode ^
      cover.hashCode ^
      privacy.hashCode ^
      user.hashCode;
}
